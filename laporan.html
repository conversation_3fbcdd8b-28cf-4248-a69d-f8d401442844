<!DOCTYPE html>
<html lang="id">
<head>
  <meta charset="UTF-8">
  <title>Buat <PERSON>an - Pengaduan Masyarakat</title>

  <!-- Menggunakan Tailwind CSS via cdn -->
  <script src="https://cdn.tailwindcss.com"></script>

  <!-- Font Awesome untuk ikon -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

  <!-- view untuk mobile perangkat mobile -->
  <meta name="viewport" content="width=device-width, initial-scale=1.0">

  <style>
    body {
      font-family: 'Poppins', sans-serif;
    }

    .form-bg {
      background-color: #f3f4f6;
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%232563eb' fill-opacity='0.05'%3E%3Cpath opacity='.5' d='M96 95h4v1h-4v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9zm-1 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9z'/%3E%3Cpath d='M6 5V0H5v5H0v1h5v94h1V6h94V5H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    }

    .gradient-text {
      background: linear-gradient(90deg, #3b82f6, #1e40af);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      color: transparent;
    }

    .file-upload-container {
      border: 2px dashed #cbd5e1;
      border-radius: 0.5rem;
      padding: 1.5rem;
      text-align: center;
      transition: all 0.3s ease;
    }

    .file-upload-container:hover {
      border-color: #3b82f6;
      background-color: #f0f9ff;
    }

    .custom-file-input {
      opacity: 0;
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      cursor: pointer;
    }

    .form-input {
      transition: all 0.3s ease;
    }

    .form-input:focus {
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
    }
  </style>
</head>
<body class="bg-gray-50 font-sans form-bg">
  <!-- Navigasi atas -->
  <nav class="bg-blue-600 text-white p-4 flex justify-between items-center shadow-md sticky top-0 z-50">
    <h1 class="text-xl font-bold">
      <i class="fas fa-comments mr-2"></i>
      Aplikasi Pengaduan Masyarakat
    </h1>
    <div id="navLinks" class="flex items-center">
      <!-- Navigation links will be inserted by JavaScript -->
    </div>
  </nav>

  <!-- Kontainer utama form -->
  <div class="container mx-auto px-4 py-8">
    <div class="max-w-3xl mx-auto bg-white p-8 rounded-xl shadow-md">
      <!-- Header dengan tombol kembali -->
      <div class="flex justify-between items-center mb-6">
        <h2 class="text-3xl font-bold gradient-text">Buat Laporan Pengaduan</h2>
        <button id="backButton" class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg transition duration-300 flex items-center">
          <i class="fas fa-arrow-left mr-2"></i> Kembali
        </button>
      </div>

      <!-- Informasi -->
      <div class="bg-blue-50 border-l-4 border-blue-500 p-4 mb-6 rounded-r-lg">
        <div class="flex">
          <div class="flex-shrink-0">
            <i class="fas fa-info-circle text-blue-500 text-xl"></i>
          </div>
          <div class="ml-3">
            <p class="text-blue-700 font-medium">Petunjuk Pengisian</p>
            <p class="text-blue-600 text-sm mt-1">Silakan isi formulir dengan lengkap dan jelas. Sertakan foto pendukung jika ada untuk memperkuat laporan Anda.</p>
          </div>
        </div>
      </div>

      <!-- Form laporan -->
      <form id="laporanForm" class="space-y-6">
        <!-- Input judul laporan -->
        <div>
          <label for="judul" class="block font-medium text-gray-700 mb-2">Judul Laporan</label>
          <input type="text" id="judul" name="judul" required
            class="w-full border border-gray-300 p-3 rounded-lg form-input"
            placeholder="Contoh: Jalan rusak parah di Jalan Merdeka">
          <p class="text-xs text-gray-500 mt-1">Berikan judul yang singkat dan jelas mengenai laporan Anda</p>
        </div>

        <!-- Textarea isi laporan -->
        <div>
          <label for="isi" class="block font-medium text-gray-700 mb-2">Isi Laporan</label>
          <textarea id="isi" name="isi" required rows="6"
            class="w-full border border-gray-300 p-3 rounded-lg form-input"
            placeholder="Jelaskan secara detail mengenai pengaduan Anda, termasuk lokasi, waktu kejadian, dan informasi penting lainnya..."></textarea>
          <p class="text-xs text-gray-500 mt-1">Semakin detail informasi yang Anda berikan, semakin mudah petugas menindaklanjuti laporan Anda</p>
        </div>

        <!-- Upload file (foto) -->
        <div>
          <label for="foto" class="block font-medium text-gray-700 mb-2">Upload Foto</label>
          <div class="relative file-upload-container">
            <input type="file" id="foto" name="foto" accept="image/*" class="custom-file-input">
            <div class="text-center">
              <i class="fas fa-cloud-upload-alt text-blue-500 text-3xl mb-2"></i>
              <p class="text-gray-700 font-medium">Klik atau seret foto ke sini</p>
              <p class="text-gray-500 text-sm mt-1">Format yang didukung: JPG, PNG, GIF (Maks. 5MB)</p>
            </div>
          </div>
          <div id="imagePreview" class="mt-4 hidden">
            <p class="text-sm font-medium text-gray-700 mb-2">Preview:</p>
            <div class="relative">
              <img id="previewImg" class="max-h-60 rounded-lg border border-gray-200" alt="Preview">
              <button type="button" id="removeImage" class="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600">
                <i class="fas fa-times"></i>
              </button>
            </div>
          </div>
        </div>

        <!-- Tombol submit -->
        <div class="pt-4">
          <button type="submit" class="w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition duration-300 flex items-center justify-center font-medium">
            <i class="fas fa-paper-plane mr-2"></i> Kirim Laporan
          </button>
        </div>
      </form>
    </div>
  </div>

  <!-- JavaScript -->
  <script src="js/app.js"></script>
  <script>
    // Check if user is logged in, redirect if not
    document.addEventListener('DOMContentLoaded', function() {
      if (!isLoggedIn()) {
        alert('Anda harus login terlebih dahulu untuk membuat laporan.');
        window.location.href = 'login.html';
        return;
      }

      // Get current user
      const username = localStorage.getItem('loggedInUser');
      const user = getCurrentUser();

      // Handle back button
      const backButton = document.getElementById('backButton');
      backButton.addEventListener('click', function() {
        window.location.href = 'user-dashboard.html';
      });

      // Handle image preview
      const fotoInput = document.getElementById('foto');
      const imagePreview = document.getElementById('imagePreview');
      const previewImg = document.getElementById('previewImg');
      const removeImageBtn = document.getElementById('removeImage');
      const fileUploadContainer = document.querySelector('.file-upload-container');

      // Add drag and drop functionality
      ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        fileUploadContainer.addEventListener(eventName, preventDefaults, false);
      });

      function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
      }

      ['dragenter', 'dragover'].forEach(eventName => {
        fileUploadContainer.addEventListener(eventName, highlight, false);
      });

      ['dragleave', 'drop'].forEach(eventName => {
        fileUploadContainer.addEventListener(eventName, unhighlight, false);
      });

      function highlight() {
        fileUploadContainer.classList.add('border-blue-500', 'bg-blue-50');
      }

      function unhighlight() {
        fileUploadContainer.classList.remove('border-blue-500', 'bg-blue-50');
      }

      fileUploadContainer.addEventListener('drop', handleDrop, false);

      function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;

        if (files.length > 0) {
          fotoInput.files = files;
          handleFileSelect(files[0]);
        }
      }

      // Handle file selection
      fotoInput.addEventListener('change', function(e) {
        if (e.target.files.length > 0) {
          const file = e.target.files[0];
          handleFileSelect(file);
        }
      });

      function handleFileSelect(file) {
        // Check file size (max 5MB)
        if (file.size > 5 * 1024 * 1024) {
          alert('Ukuran file terlalu besar. Maksimal 5MB.');
          fotoInput.value = '';
          return;
        }

        // Check file type
        const validTypes = ['image/jpeg', 'image/png', 'image/gif'];
        if (!validTypes.includes(file.type)) {
          alert('Format file tidak didukung. Gunakan JPG, PNG, atau GIF.');
          fotoInput.value = '';
          return;
        }

        const reader = new FileReader();

        reader.onload = function(e) {
          previewImg.src = e.target.result;
          imagePreview.classList.remove('hidden');
          fileUploadContainer.classList.add('border-green-500');
        };

        reader.readAsDataURL(file);
      }

      // Handle remove image button
      removeImageBtn.addEventListener('click', function() {
        imagePreview.classList.add('hidden');
        fotoInput.value = '';
        fileUploadContainer.classList.remove('border-green-500');
      });

      // Handle form submission with validation
      const laporanForm = document.getElementById('laporanForm');
      const judulInput = document.getElementById('judul');
      const isiInput = document.getElementById('isi');

      laporanForm.addEventListener('submit', function(e) {
        e.preventDefault();

        // Basic validation
        if (judulInput.value.trim().length < 5) {
          alert('Judul laporan terlalu pendek. Minimal 5 karakter.');
          judulInput.focus();
          return;
        }

        if (isiInput.value.trim().length < 20) {
          alert('Isi laporan terlalu pendek. Berikan penjelasan yang lebih detail.');
          isiInput.focus();
          return;
        }

        // Show loading state
        const submitBtn = laporanForm.querySelector('button[type="submit"]');
        const originalBtnText = submitBtn.innerHTML;
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Mengirim...';

        const judul = judulInput.value;
        const isi = isiInput.value;
        const fotoFile = fotoInput.files[0];

        // Convert image to base64 if exists
        if (fotoFile) {
          const reader = new FileReader();

          reader.onload = function(e) {
            const fotoBase64 = e.target.result;

            // Create report object
            const report = {
              judul: judul,
              isi: isi,
              foto: fotoBase64,
              pelapor: username,
              tanggal: new Date().toISOString(),
              status: 'menunggu'
            };

            // Save report
            addReport(report);

            // Show success message
            showSuccessMessage();
          };

          reader.readAsDataURL(fotoFile);
        } else {
          // Create report without image
          const report = {
            judul: judul,
            isi: isi,
            foto: null,
            pelapor: username,
            tanggal: new Date().toISOString(),
            status: 'menunggu'
          };

          // Save report
          addReport(report);

          // Show success message
          showSuccessMessage();
        }

        function showSuccessMessage() {
          // Replace form with success message
          const formContainer = laporanForm.parentElement;
          formContainer.innerHTML = `
            <div class="text-center py-8">
              <div class="bg-green-100 w-20 h-20 mx-auto rounded-full flex items-center justify-center mb-4">
                <i class="fas fa-check-circle text-green-600 text-4xl"></i>
              </div>
              <h3 class="text-2xl font-bold text-gray-800 mb-2">Laporan Berhasil Dikirim!</h3>
              <p class="text-gray-600 mb-6">Terima kasih atas laporan Anda. Petugas kami akan segera memproses laporan Anda.</p>
              <div class="flex justify-center space-x-4">
                <a href="user-dashboard.html" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition duration-300 flex items-center">
                  <i class="fas fa-home mr-2"></i> Kembali ke Dashboard
                </a>
                <button id="newReportBtn" class="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition duration-300 flex items-center">
                  <i class="fas fa-plus-circle mr-2"></i> Buat Laporan Baru
                </button>
              </div>
            </div>
          `;

          // Add event listener to new report button
          document.getElementById('newReportBtn').addEventListener('click', function() {
            window.location.reload();
          });
        }
      });
    });
  </script>
</body>
</html>
