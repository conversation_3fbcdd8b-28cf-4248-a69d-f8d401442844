<?php
// logout.php - Handle user logout for Pengaduan Masyarakat

// Set content type to JSON
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET');
header('Access-Control-Allow-Headers: Content-Type');

// Start session
session_start();

// Response function
function sendResponse($success, $message, $data = null) {
    echo json_encode([
        'success' => $success,
        'message' => $message,
        'data' => $data
    ]);
    exit;
}

try {
    // Clear all session data
    $_SESSION = array();
    
    // Destroy the session cookie
    if (ini_get("session.use_cookies")) {
        $params = session_get_cookie_params();
        setcookie(session_name(), '', time() - 42000,
            $params["path"], $params["domain"],
            $params["secure"], $params["httponly"]
        );
    }
    
    // Destroy the session
    session_destroy();
    
    sendResponse(true, 'Logout berhasil!', [
        'redirect_url' => 'index.html'
    ]);
    
} catch (Exception $e) {
    error_log("Logout error: " . $e->getMessage());
    sendResponse(false, 'Terjadi kesalahan saat logout.');
}
?>
