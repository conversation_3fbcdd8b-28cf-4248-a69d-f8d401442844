// login-php.js - Handle login with PHP backend

// <PERSON>le login form submission with PHP backend
function handleLoginPHP(e) {
  e.preventDefault();

  const username = document.getElementById('username').value.trim();
  const password = document.getElementById('password').value;

  // Basic client-side validation
  if (!username || !password) {
    alert('Username dan password harus diisi!');
    return;
  }

  if (username.length < 3) {
    alert('Username minimal 3 karakter!');
    return;
  }

  // Disable submit button to prevent double submission
  const submitButton = document.querySelector('button[type="submit"]');
  const originalText = submitButton.innerHTML;
  submitButton.disabled = true;
  submitButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Login...';

  // Prepare data for PHP backend
  const formData = {
    username: username,
    password: password
  };

  // Send data to PHP backend
  fetch('login.php', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(formData)
  })
  .then(response => {
    console.log('Login response status:', response.status);
    return response.text();
  })
  .then(text => {
    console.log('Login raw response:', text);
    try {
      const data = JSON.parse(text);
      if (data.success) {
        alert(data.message);
        
        // Store user data in localStorage for session management
        localStorage.setItem('loggedInUser', JSON.stringify({
          user_id: data.data.user_id,
          username: data.data.username,
          nama: data.data.nama,
          role: data.data.role,
          logged_in: true
        }));
        
        // Redirect based on response
        if (data.data.redirect_url) {
          window.location.href = data.data.redirect_url;
        } else {
          // Fallback redirect based on role
          if (data.data.role === 'admin' || data.data.role === 'petugas') {
            window.location.href = 'dashboard.html';
          } else {
            window.location.href = 'user-dashboard.html';
          }
        }
      } else {
        alert('Error: ' + data.message);
      }
    } catch (e) {
      console.error('JSON parse error:', e);
      console.error('Raw response:', text);
      alert('Terjadi kesalahan dalam memproses response server.');
    }
  })
  .catch(error => {
    console.error('Network error:', error);
    alert('Terjadi kesalahan jaringan. Silakan coba lagi nanti.');
  })
  .finally(() => {
    // Re-enable submit button
    submitButton.disabled = false;
    submitButton.innerHTML = originalText;
  });
}

// Alternative function for form data submission (if JSON doesn't work)
function handleLoginPHPFormData(e) {
  e.preventDefault();

  const username = document.getElementById('username').value.trim();
  const password = document.getElementById('password').value;

  // Basic client-side validation
  if (!username || !password) {
    alert('Username dan password harus diisi!');
    return;
  }

  // Disable submit button
  const submitButton = document.querySelector('button[type="submit"]');
  const originalText = submitButton.innerHTML;
  submitButton.disabled = true;
  submitButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Login...';

  // Create FormData object
  const formData = new FormData();
  formData.append('username', username);
  formData.append('password', password);

  // Send data to PHP backend
  fetch('login.php', {
    method: 'POST',
    body: formData
  })
  .then(response => {
    console.log('FormData login response status:', response.status);
    return response.text();
  })
  .then(text => {
    console.log('FormData login raw response:', text);
    try {
      const data = JSON.parse(text);
      if (data.success) {
        alert(data.message);
        
        // Store user data in localStorage for session management
        localStorage.setItem('loggedInUser', JSON.stringify({
          user_id: data.data.user_id,
          username: data.data.username,
          nama: data.data.nama,
          role: data.data.role,
          logged_in: true
        }));
        
        // Redirect based on response
        if (data.data.redirect_url) {
          window.location.href = data.data.redirect_url;
        } else {
          // Fallback redirect based on role
          if (data.data.role === 'admin' || data.data.role === 'petugas') {
            window.location.href = 'dashboard.html';
          } else {
            window.location.href = 'user-dashboard.html';
          }
        }
      } else {
        alert('Error: ' + data.message);
      }
    } catch (e) {
      console.error('JSON parse error:', e);
      console.error('Raw response:', text);
      alert('Terjadi kesalahan dalam memproses response server.');
    }
  })
  .catch(error => {
    console.error('Network error:', error);
    alert('Terjadi kesalahan jaringan. Silakan coba lagi nanti.');
  })
  .finally(() => {
    // Re-enable submit button
    submitButton.disabled = false;
    submitButton.innerHTML = originalText;
  });
}

// Initialize PHP login when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
  const loginForm = document.getElementById('loginForm');
  if (loginForm) {
    // Clear any existing event listeners
    const newForm = loginForm.cloneNode(true);
    loginForm.parentNode.replaceChild(newForm, loginForm);
    
    // Add PHP backend event listener to the new form
    newForm.addEventListener('submit', handleLoginPHP);
    
    console.log('PHP login handler attached');
  }
});

// Function to switch between localStorage and PHP backend
function switchToPhpLogin() {
  const loginForm = document.getElementById('loginForm');
  if (loginForm) {
    // Remove localStorage handler
    loginForm.removeEventListener('submit', handleLogin);
    
    // Add PHP handler
    loginForm.addEventListener('submit', handleLoginPHP);
    
    console.log('Switched to PHP backend for login');
  }
}

// Function to switch back to localStorage
function switchToLocalStorageLogin() {
  const loginForm = document.getElementById('loginForm');
  if (loginForm) {
    // Remove PHP handler
    loginForm.removeEventListener('submit', handleLoginPHP);
    
    // Add localStorage handler
    loginForm.addEventListener('submit', handleLogin);
    
    console.log('Switched to localStorage for login');
  }
}

// Check if user is logged in (compatible with both systems)
function isLoggedIn() {
  const userData = localStorage.getItem('loggedInUser');
  if (userData) {
    try {
      const user = JSON.parse(userData);
      return user.logged_in === true;
    } catch (e) {
      // Fallback for old localStorage format
      return localStorage.getItem('loggedInUser') !== null;
    }
  }
  return false;
}

// Get current user data (compatible with both systems)
function getCurrentUser() {
  const userData = localStorage.getItem('loggedInUser');
  if (userData) {
    try {
      return JSON.parse(userData);
    } catch (e) {
      // Fallback for old localStorage format
      const username = localStorage.getItem('loggedInUser');
      const userDataOld = localStorage.getItem(username);
      if (userDataOld) {
        return JSON.parse(userDataOld);
      }
    }
  }
  return null;
}
