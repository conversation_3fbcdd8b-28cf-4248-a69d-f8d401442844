<?php
// test_login.php - Test login functionality
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Test Login Functionality</h2>";

// Include database configuration
require_once 'config/database.php';

try {
    // Get users from database for testing
    $pdo = getDatabaseConnection();
    $stmt = $pdo->query("SELECT id, nama, username, role, created_at FROM users ORDER BY created_at DESC LIMIT 10");
    $users = $stmt->fetchAll();
    
    echo "<h3>Available Users for Testing:</h3>";
    if (!empty($users)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f2f2f2;'>";
        echo "<th style='padding: 8px;'>ID</th>";
        echo "<th style='padding: 8px;'>Nama</th>";
        echo "<th style='padding: 8px;'>Username</th>";
        echo "<th style='padding: 8px;'>Role</th>";
        echo "<th style='padding: 8px;'>Created At</th>";
        echo "<th style='padding: 8px;'>Test Login</th>";
        echo "</tr>";
        
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>{$user['id']}</td>";
            echo "<td style='padding: 8px;'>{$user['nama']}</td>";
            echo "<td style='padding: 8px;'>{$user['username']}</td>";
            echo "<td style='padding: 8px;'>{$user['role']}</td>";
            echo "<td style='padding: 8px;'>{$user['created_at']}</td>";
            echo "<td style='padding: 8px;'>";
            echo "<button onclick=\"testLogin('{$user['username']}', 'password123')\" style='padding: 4px 8px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer;'>Test Login</button>";
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<p><strong>Note:</strong> Default password untuk semua user adalah 'password123' (jika dibuat melalui test)</p>";
    } else {
        echo "<p style='color: red;'>Tidak ada user ditemukan. Silakan daftar user baru terlebih dahulu.</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error mengakses database: " . $e->getMessage() . "</p>";
}

// Manual login test form
echo "<hr>";
echo "<h3>Manual Login Test:</h3>";
echo "<form id='manualLoginForm' style='border: 1px solid #ddd; padding: 20px; background: #f9f9f9; max-width: 400px;'>";
echo "<div style='margin-bottom: 15px;'>";
echo "<label for='testUsername' style='display: block; margin-bottom: 5px; font-weight: bold;'>Username:</label>";
echo "<input type='text' id='testUsername' name='username' required style='width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px;'>";
echo "</div>";
echo "<div style='margin-bottom: 15px;'>";
echo "<label for='testPassword' style='display: block; margin-bottom: 5px; font-weight: bold;'>Password:</label>";
echo "<input type='password' id='testPassword' name='password' required style='width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px;'>";
echo "</div>";
echo "<button type='submit' style='padding: 10px 20px; background: #007cba; color: white; border: none; border-radius: 4px; cursor: pointer;'>Test Login</button>";
echo "</form>";

echo "<div id='loginResult' style='margin-top: 20px;'></div>";

echo "<hr>";
echo "<h3>Default Test Accounts:</h3>";
echo "<p>Jika Anda sudah menjalankan setup_database.php, tersedia akun default:</p>";
echo "<ul>";
echo "<li><strong>Admin:</strong> username = 'admin', password = 'password'</li>";
echo "<li><strong>Petugas:</strong> username = 'petugas', password = 'password'</li>";
echo "</ul>";

echo "<h3>Links:</h3>";
echo "<ul>";
echo "<li><a href='login.html'>Login Form</a></li>";
echo "<li><a href='register.html'>Register New User</a></li>";
echo "<li><a href='setup_database.php'>Setup Database</a></li>";
echo "<li><a href='debug_register.php'>Debug Register</a></li>";
echo "</ul>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; border: 1px solid #ddd; }
th { background-color: #f2f2f2; }
a { color: #007cba; text-decoration: none; }
a:hover { text-decoration: underline; }
.result { margin: 20px 0; padding: 15px; border-radius: 4px; }
.success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
.error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
.debug { background: #f8f9fa; border: 1px solid #dee2e6; color: #495057; font-family: monospace; white-space: pre-wrap; }
</style>

<script>
// Function to test login with specific credentials
function testLogin(username, password) {
    const resultDiv = document.getElementById('loginResult');
    resultDiv.innerHTML = '<div class="debug">Testing login for: ' + username + '</div>';
    
    const formData = {
        username: username,
        password: password
    };
    
    fetch('login.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
    })
    .then(response => {
        console.log('Login test response status:', response.status);
        return response.text();
    })
    .then(text => {
        console.log('Login test raw response:', text);
        
        try {
            const data = JSON.parse(text);
            
            if (data.success) {
                resultDiv.innerHTML = `
                    <div class="success">
                        <strong>✓ Login Success:</strong> ${data.message}
                        <br><strong>User:</strong> ${data.data.nama} (${data.data.role})
                        <br><strong>Redirect:</strong> ${data.data.redirect_url}
                    </div>
                    <div class="debug">Response: ${JSON.stringify(data, null, 2)}</div>
                `;
            } else {
                resultDiv.innerHTML = `
                    <div class="error">
                        <strong>✗ Login Failed:</strong> ${data.message}
                    </div>
                    <div class="debug">Response: ${JSON.stringify(data, null, 2)}</div>
                `;
            }
        } catch (e) {
            resultDiv.innerHTML = `
                <div class="error">
                    <strong>✗ JSON Parse Error:</strong> ${e.message}
                </div>
                <div class="debug">Raw response: ${text}</div>
            `;
        }
    })
    .catch(error => {
        console.error('Login test error:', error);
        resultDiv.innerHTML = `
            <div class="error">
                <strong>✗ Network Error:</strong> ${error.message}
            </div>
        `;
    });
}

// Handle manual login form
document.getElementById('manualLoginForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const username = document.getElementById('testUsername').value;
    const password = document.getElementById('testPassword').value;
    
    testLogin(username, password);
});
</script>
