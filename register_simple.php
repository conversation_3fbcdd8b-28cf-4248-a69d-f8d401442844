<?php
// register_simple.php - Simplified registration for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set content type to JSON
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Simple response function
function sendResponse($success, $message, $data = null) {
    $response = [
        'success' => $success,
        'message' => $message,
        'data' => $data,
        'debug' => [
            'timestamp' => date('Y-m-d H:i:s'),
            'method' => $_SERVER['REQUEST_METHOD'],
            'content_type' => $_SERVER['CONTENT_TYPE'] ?? 'not set'
        ]
    ];
    echo json_encode($response, JSON_PRETTY_PRINT);
    exit;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendResponse(false, 'Method not allowed. Use POST.');
}

try {
    // Log request details
    $rawInput = file_get_contents('php://input');
    error_log("Raw input: " . $rawInput);
    error_log("POST data: " . json_encode($_POST));
    
    // Get input data
    $input = json_decode($rawInput, true);
    if (empty($input)) {
        $input = $_POST;
    }
    
    if (empty($input)) {
        sendResponse(false, 'No input data received', [
            'raw_input' => $rawInput,
            'post_data' => $_POST,
            'json_decode_error' => json_last_error_msg()
        ]);
    }
    
    // Check required fields
    $required = ['nama', 'username', 'password', 'confirmPassword'];
    $missing = [];
    
    foreach ($required as $field) {
        if (empty($input[$field])) {
            $missing[] = $field;
        }
    }
    
    if (!empty($missing)) {
        sendResponse(false, 'Missing required fields: ' . implode(', ', $missing), [
            'received_data' => array_keys($input),
            'missing_fields' => $missing
        ]);
    }
    
    // Basic validation
    if ($input['password'] !== $input['confirmPassword']) {
        sendResponse(false, 'Password dan konfirmasi password tidak cocok');
    }
    
    if (strlen($input['password']) < 6) {
        sendResponse(false, 'Password minimal 6 karakter');
    }
    
    if (strlen($input['username']) < 3) {
        sendResponse(false, 'Username minimal 3 karakter');
    }
    
    // Database connection
    $host = 'localhost';
    $dbname = 'pengaduan_masyarakat';
    $username = 'root';
    $password = '';
    
    try {
        $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        error_log("Database connection successful");
    } catch (PDOException $e) {
        sendResponse(false, 'Database connection failed: ' . $e->getMessage());
    }
    
    // Check if username exists
    $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ?");
    $stmt->execute([strtolower(trim($input['username']))]);
    
    if ($stmt->fetch()) {
        sendResponse(false, 'Username sudah digunakan');
    }
    
    // Insert new user
    $nama = trim($input['nama']);
    $username = strtolower(trim($input['username']));
    $hashedPassword = password_hash($input['password'], PASSWORD_DEFAULT);
    
    $stmt = $pdo->prepare("
        INSERT INTO users (nama, username, password, role, created_at) 
        VALUES (?, ?, ?, 'user', NOW())
    ");
    
    $result = $stmt->execute([$nama, $username, $hashedPassword]);
    
    if ($result) {
        $userId = $pdo->lastInsertId();
        error_log("User created successfully with ID: " . $userId);
        
        sendResponse(true, 'Pendaftaran berhasil!', [
            'user_id' => $userId,
            'username' => $username,
            'nama' => $nama
        ]);
    } else {
        sendResponse(false, 'Gagal menyimpan user ke database');
    }
    
} catch (PDOException $e) {
    error_log("PDO Error: " . $e->getMessage());
    sendResponse(false, 'Database error: ' . $e->getMessage());
} catch (Exception $e) {
    error_log("General Error: " . $e->getMessage());
    sendResponse(false, 'System error: ' . $e->getMessage());
}
?>
