<!DOCTYPE html>
<html lang="id">
<head>
  <meta charset="UTF-8">
  <title>Generate Laporan - Pengaduan Masyarakat</title>

  <!-- Menggunakan Tailwind CSS untuk styling -->
  <script src="https://cdn.tailwindcss.com"></script>

  <!-- Font Awesome untuk ikon -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

  <!-- view untuk mobile perangkat mobile -->
  <meta name="viewport" content="width=device-width, initial-scale=1.0">

  <style>
    body {
      font-family: 'Poppins', sans-serif;
    }

    .report-bg {
      background-color: #f3f4f6;
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%232563eb' fill-opacity='0.05'%3E%3Cpath opacity='.5' d='M96 95h4v1h-4v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9zm-1 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9z'/%3E%3Cpath d='M6 5V0H5v5H0v1h5v94h1V6h94V5H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    }

    .gradient-text {
      background: linear-gradient(90deg, #3b82f6, #1e40af);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      color: transparent;
    }

    .status-badge {
      display: inline-block;
      padding: 0.25rem 0.75rem;
      border-radius: 9999px;
      font-size: 0.75rem;
      font-weight: 600;
    }

    .action-button {
      transition: all 0.3s ease;
    }

    .action-button:hover {
      transform: translateY(-2px);
    }

    .table-container {
      border-radius: 0.75rem;
      overflow: hidden;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }
  </style>
</head>
<body class="bg-gray-50 font-sans report-bg">

  <!-- Navigasi atas -->
  <nav class="bg-blue-600 text-white p-4 flex justify-between items-center shadow-md sticky top-0 z-50">
    <h1 class="text-xl font-bold">
      <i class="fas fa-file-alt mr-2"></i>
      Generate Laporan
    </h1>
    <div id="navLinks" class="flex items-center">
      <!-- Navigation links will be inserted by JavaScript -->
    </div>
  </nav>

  <!-- Konten utama -->
  <main class="container mx-auto px-4 py-8">
    <div class="bg-white p-6 rounded-xl shadow-md">
      <!-- Header dengan tombol kembali -->
      <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold gradient-text">Generate Laporan</h2>
        <button id="backButton" class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg transition duration-300 flex items-center">
          <i class="fas fa-arrow-left mr-2"></i> Kembali ke Dashboard
        </button>
      </div>

      <!-- Info box -->
      <div class="bg-blue-50 border-l-4 border-blue-500 p-4 mb-6 rounded-r-lg">
        <div class="flex">
          <div class="flex-shrink-0">
            <i class="fas fa-info-circle text-blue-500 text-xl"></i>
          </div>
          <div class="ml-3">
            <p class="text-blue-700 font-medium">Informasi Laporan</p>
            <p class="text-blue-600 text-sm mt-1">Gunakan filter di bawah untuk menyaring laporan berdasarkan status dan rentang tanggal. Anda dapat mencetak laporan atau mengekspornya ke CSV.</p>
          </div>
        </div>
      </div>

      <!-- Filter options -->
      <div class="bg-gray-50 p-6 rounded-xl mb-6 border border-gray-100">
        <h3 class="text-lg font-semibold mb-4 text-gray-800 flex items-center">
          <i class="fas fa-filter text-blue-600 mr-2"></i> Filter Laporan
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <!-- Status filter -->
          <div>
            <label for="statusFilter" class="block text-gray-700 font-medium mb-2">Status</label>
            <select id="statusFilter" class="w-full border border-gray-300 p-3 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition">
              <option value="all">Semua Status</option>
              <option value="menunggu">Menunggu</option>
              <option value="terverifikasi">Terverifikasi</option>
              <option value="ditolak">Ditolak</option>
              <option value="selesai">Selesai</option>
            </select>
          </div>

          <!-- Date range filter -->
          <div>
            <label for="startDate" class="block text-gray-700 font-medium mb-2">Tanggal Mulai</label>
            <input type="date" id="startDate" class="w-full border border-gray-300 p-3 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition">
          </div>

          <div>
            <label for="endDate" class="block text-gray-700 font-medium mb-2">Tanggal Akhir</label>
            <input type="date" id="endDate" class="w-full border border-gray-300 p-3 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition">
          </div>
        </div>

        <div class="mt-6 flex flex-wrap gap-3">
          <button id="filterButton" class="action-button bg-blue-600 text-white px-5 py-3 rounded-lg hover:bg-blue-700 transition duration-300 flex items-center font-medium">
            <i class="fas fa-search mr-2"></i> Terapkan Filter
          </button>
          <button id="resetButton" class="action-button bg-gray-300 text-gray-800 px-5 py-3 rounded-lg hover:bg-gray-400 transition duration-300 flex items-center font-medium">
            <i class="fas fa-redo-alt mr-2"></i> Reset
          </button>
          <button id="printButton" class="action-button bg-green-600 text-white px-5 py-3 rounded-lg hover:bg-green-700 transition duration-300 flex items-center font-medium">
            <i class="fas fa-print mr-2"></i> Cetak Laporan
          </button>
          <button id="csvButton" class="action-button bg-purple-600 text-white px-5 py-3 rounded-lg hover:bg-purple-700 transition duration-300 flex items-center font-medium">
            <i class="fas fa-file-csv mr-2"></i> Export CSV
          </button>
        </div>
      </div>

      <!-- Report table -->
      <div id="reportTableContainer">
        <h3 class="text-lg font-semibold mb-4 text-gray-800 flex items-center">
          <i class="fas fa-list text-blue-600 mr-2"></i> Daftar Laporan
        </h3>
        <div class="overflow-x-auto table-container">
          <table class="w-full" id="reportTable">
            <thead class="bg-blue-50">
              <tr>
                <th class="text-left px-6 py-3 text-gray-700">ID</th>
                <th class="text-left px-6 py-3 text-gray-700">Judul</th>
                <th class="text-left px-6 py-3 text-gray-700">Pelapor</th>
                <th class="text-left px-6 py-3 text-gray-700">Tanggal</th>
                <th class="text-left px-6 py-3 text-gray-700">Status</th>
              </tr>
            </thead>
            <tbody id="reportTableBody" class="divide-y divide-gray-100">
              <!-- Report rows will be inserted by JavaScript -->
            </tbody>
          </table>
        </div>
      </div>

      <!-- Empty state message -->
      <div id="emptyState" class="hidden text-center p-10 bg-white rounded-xl shadow-md mt-6 border border-gray-100">
        <div class="mb-6">
          <img src="https://img.freepik.com/free-vector/no-data-concept-illustration_114360-536.jpg" alt="Tidak ada laporan" class="w-64 h-64 mx-auto">
        </div>
        <h3 class="text-xl font-semibold text-gray-800 mb-2">Tidak Ada Laporan</h3>
        <p class="text-gray-500 mb-2 max-w-md mx-auto">Tidak ada laporan yang ditemukan dengan filter yang dipilih.</p>
        <p class="text-gray-500 max-w-md mx-auto">Coba ubah filter untuk melihat laporan lainnya.</p>
      </div>
    </div>
  </main>

  <!-- Print template (hidden) -->
  <div id="printTemplate" class="hidden">
    <div id="printContent"></div>
  </div>

  <!-- JavaScript -->
  <script src="js/app.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Check if user is admin, redirect if not
      if (!isAdmin()) {
        alert('Hanya admin yang dapat mengakses halaman ini.');
        window.location.href = 'dashboard.html';
        return;
      }

      // Initialize date inputs with default values (last 30 days)
      const today = new Date();
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(today.getDate() - 30);

      document.getElementById('endDate').valueAsDate = today;
      document.getElementById('startDate').valueAsDate = thirtyDaysAgo;

      // Get all reports
      let allReports = getAllReports();
      let filteredReports = [...allReports];

      // Handle back button
      const backButton = document.getElementById('backButton');
      backButton.addEventListener('click', function() {
        window.location.href = 'dashboard.html';
      });

      // Apply filters
      function applyFilters() {
        const statusFilter = document.getElementById('statusFilter').value;
        const startDate = new Date(document.getElementById('startDate').value);
        const endDate = new Date(document.getElementById('endDate').value);
        endDate.setHours(23, 59, 59); // Set to end of day

        filteredReports = allReports.filter(report => {
          const reportDate = new Date(report.tanggal);
          const matchesStatus = statusFilter === 'all' || report.status === statusFilter;
          const matchesDate = reportDate >= startDate && reportDate <= endDate;

          return matchesStatus && matchesDate;
        });

        displayReports(filteredReports);
      }

      // Display reports
      function displayReports(reports) {
        const reportTableBody = document.getElementById('reportTableBody');
        const emptyState = document.getElementById('emptyState');
        const reportTableContainer = document.getElementById('reportTableContainer');

        if (reports.length > 0) {
          let reportsHTML = '';

          reports.forEach(report => {
            // Determine status text
            let statusText = 'Menunggu';

            if (report.status === 'terverifikasi') {
              statusText = 'Terverifikasi';
            } else if (report.status === 'ditolak') {
              statusText = 'Ditolak';
            } else if (report.status === 'selesai') {
              statusText = 'Selesai';
            }

            // No need to check for responses anymore since we removed the Tanggapan column

            // Get reporter's full name
            const pelaporFullName = getUserFullName(report.pelapor);

            // Determine status class and icon
            let statusClass = '';
            let statusIcon = '';

            if (report.status === 'menunggu') {
              statusClass = 'text-yellow-600 bg-yellow-50';
              statusIcon = 'clock';
            } else if (report.status === 'terverifikasi') {
              statusClass = 'text-green-600 bg-green-50';
              statusIcon = 'check-circle';
            } else if (report.status === 'ditolak') {
              statusClass = 'text-red-600 bg-red-50';
              statusIcon = 'times-circle';
            } else if (report.status === 'selesai') {
              statusClass = 'text-blue-600 bg-blue-50';
              statusIcon = 'check-double';
            }

            // Create table row with improved styling
            reportsHTML += `
              <tr class="hover:bg-blue-50 transition-colors duration-150">
                <td class="px-6 py-4 text-gray-800">${report.id}</td>
                <td class="px-6 py-4">
                  <div class="font-medium text-gray-800">${report.judul}</div>
                </td>
                <td class="px-6 py-4">
                  <div class="flex items-center">
                    <div class="bg-blue-100 w-8 h-8 rounded-full flex items-center justify-center mr-2">
                      <i class="fas fa-user text-blue-600"></i>
                    </div>
                    <span class="text-gray-800">${pelaporFullName}</span>
                  </div>
                </td>
                <td class="px-6 py-4 text-gray-600">${formatDate(report.tanggal)}</td>
                <td class="px-6 py-4">
                  <span class="px-3 py-1 rounded-full text-xs font-semibold ${statusClass}">
                    <i class="fas fa-${statusIcon} mr-1"></i> ${statusText}
                  </span>
                </td>
              </tr>
            `;
          });

          reportTableBody.innerHTML = reportsHTML;
          reportTableContainer.classList.remove('hidden');
          emptyState.classList.add('hidden');
        } else {
          reportTableContainer.classList.add('hidden');
          emptyState.classList.remove('hidden');
        }
      }

      // Handle filter button click
      document.getElementById('filterButton').addEventListener('click', applyFilters);

      // Handle reset button click
      document.getElementById('resetButton').addEventListener('click', function() {
        document.getElementById('statusFilter').value = 'all';

        const today = new Date();
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(today.getDate() - 30);

        document.getElementById('endDate').valueAsDate = today;
        document.getElementById('startDate').valueAsDate = thirtyDaysAgo;

        applyFilters();
      });

      // Handle print button click
      document.getElementById('printButton').addEventListener('click', function() {
        if (filteredReports.length === 0) {
          alert('Tidak ada laporan untuk dicetak.');
          return;
        }

        const printContent = document.getElementById('printContent');
        const statusFilter = document.getElementById('statusFilter').value;
        const startDate = formatDate(document.getElementById('startDate').value);
        const endDate = formatDate(document.getElementById('endDate').value);

        let statusText = 'Semua Status';
        if (statusFilter === 'menunggu') statusText = 'Menunggu';
        if (statusFilter === 'terverifikasi') statusText = 'Terverifikasi';
        if (statusFilter === 'ditolak') statusText = 'Ditolak';
        if (statusFilter === 'selesai') statusText = 'Selesai';

        // Create print content
        printContent.innerHTML = `
          <style>
            body { font-family: Arial, sans-serif; }
            table { width: 100%; border-collapse: collapse; margin-top: 20px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; }
            h1, h2 { text-align: center; }
            .header { margin-bottom: 20px; }
            .filter-info { margin-bottom: 20px; }
          </style>
          <div class="header">
            <h1>Laporan Pengaduan Masyarakat</h1>
            <p>Tanggal Cetak: ${formatDate(new Date().toISOString())}</p>
          </div>
          <div class="filter-info">
            <p><strong>Filter:</strong> ${statusText}</p>
            <p><strong>Periode:</strong> ${startDate} - ${endDate}</p>
            <p><strong>Jumlah Laporan:</strong> ${filteredReports.length}</p>
          </div>
          <table>
            <thead>
              <tr>
                <th>ID</th>
                <th>Judul</th>
                <th>Pelapor</th>
                <th>Tanggal</th>
                <th>Status</th>
              </tr>
            </thead>
            <tbody>
              ${filteredReports.map(report => {
                let statusText = 'Menunggu';
                if (report.status === 'terverifikasi') statusText = 'Terverifikasi';
                if (report.status === 'ditolak') statusText = 'Ditolak';
                if (report.status === 'selesai') statusText = 'Selesai';

                // Get reporter's full name
                const pelaporFullName = getUserFullName(report.pelapor);

                return `
                  <tr>
                    <td>${report.id}</td>
                    <td>${report.judul}</td>
                    <td>${pelaporFullName}</td>
                    <td>${formatDate(report.tanggal)}</td>
                    <td>${statusText}</td>
                  </tr>
                `;
              }).join('')}
            </tbody>
          </table>
        `;

        // Open print dialog
        const printWindow = window.open('', '_blank');
        printWindow.document.write(printContent.innerHTML);
        printWindow.document.close();
        printWindow.focus();
        printWindow.print();
        printWindow.close();
      });

      // Handle CSV export button click
      document.getElementById('csvButton').addEventListener('click', function() {
        if (filteredReports.length === 0) {
          alert('Tidak ada laporan untuk diekspor.');
          return;
        }

        try {
          // Create Excel-compatible CSV content
          // Using semicolons as separators for better Excel compatibility in many locales
          const separator = ";";

          // Always quote all fields for consistent Excel formatting
          function formatExcelField(value) {
            // Convert to string and trim whitespace
            let field = String(value || '').trim();
            // Replace any double quotes with two double quotes (Excel escaping)
            field = field.replace(/"/g, '""');
            // Always wrap in quotes for consistent formatting
            return `"${field}"`;
          }

          // Create CSV header with proper formatting (removed Jumlah Tanggapan)
          const headers = ['ID', 'Judul', 'Pelapor', 'Tanggal', 'Status'];
          let csvContent = headers.map(formatExcelField).join(separator) + '\r\n';

          // Process each report
          filteredReports.forEach(report => {
            // Get status text
            let statusText = 'Menunggu';
            if (report.status === 'terverifikasi') statusText = 'Terverifikasi';
            if (report.status === 'ditolak') statusText = 'Ditolak';
            if (report.status === 'selesai') statusText = 'Selesai';

            // No need to get tanggapan count anymore

            // Get reporter's full name
            const pelaporFullName = getUserFullName(report.pelapor);

            // Format date for Excel (using dd/MM/yyyy format)
            const reportDate = new Date(report.tanggal);
            const day = String(reportDate.getDate()).padStart(2, '0');
            const month = String(reportDate.getMonth() + 1).padStart(2, '0');
            const year = reportDate.getFullYear();
            const formattedDate = `${day}/${month}/${year}`;

            // Create row data array (removed Jumlah Tanggapan)
            const rowData = [
              report.id,
              report.judul,
              pelaporFullName,
              formattedDate,
              statusText
            ];

            // Format each field and join with semicolons
            const csvRow = rowData.map(formatExcelField).join(separator);

            // Add row to CSV content with Windows-style line endings
            csvContent += csvRow + '\r\n';
          });

          // Use BOM for Excel to properly detect UTF-8
          const BOM = '\uFEFF';
          const csvContentWithBOM = BOM + csvContent;

          // Create download link with proper MIME type for Excel
          // Using application/vnd.ms-excel to hint to the browser this is an Excel file
          const blob = new Blob([csvContentWithBOM], { type: 'application/vnd.ms-excel;charset=utf-8' });
          const url = URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.setAttribute('href', url);

          // Create filename with current date
          const today = new Date();
          const dateStr = today.toISOString().split('T')[0];
          link.setAttribute('download', `laporan_pengaduan_${dateStr}.csv`);

          // Trigger download
          document.body.appendChild(link);
          link.click();

          // Clean up
          setTimeout(() => {
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
          }, 100);
        } catch (error) {
          console.error("Error generating CSV:", error);
          alert("Terjadi kesalahan saat membuat file CSV. Silakan coba lagi.");
        }
      });

      // Initial display
      applyFilters();
    });
  </script>
</body>
</html>
