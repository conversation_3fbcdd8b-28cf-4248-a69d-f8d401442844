<?php
// register_clean.php - Clean version of registration
// Clean output buffer
if (ob_get_level()) {
    ob_clean();
}

// Disable any output
error_reporting(0);
ini_set('display_errors', 0);

// Set JSON header
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Response function
function sendCleanResponse($success, $message, $data = null) {
    // Ensure clean output
    if (ob_get_level()) {
        ob_clean();
    }
    
    $response = [
        'success' => $success,
        'message' => $message,
        'data' => $data
    ];
    
    echo json_encode($response);
    exit;
}

// Only allow POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendCleanResponse(false, 'Method not allowed');
}

try {
    // Get input
    $rawInput = file_get_contents('php://input');
    $input = json_decode($rawInput, true);
    
    if (empty($input)) {
        $input = $_POST;
    }
    
    if (empty($input)) {
        sendCleanResponse(false, 'No data received');
    }
    
    // Validate required fields
    $required = ['nama', 'username', 'password', 'confirmPassword'];
    foreach ($required as $field) {
        if (empty($input[$field])) {
            sendCleanResponse(false, "Field $field harus diisi");
        }
    }
    
    // Validate password
    if ($input['password'] !== $input['confirmPassword']) {
        sendCleanResponse(false, 'Password dan konfirmasi password tidak cocok');
    }
    
    if (strlen($input['password']) < 6) {
        sendCleanResponse(false, 'Password minimal 6 karakter');
    }
    
    if (strlen($input['username']) < 3) {
        sendCleanResponse(false, 'Username minimal 3 karakter');
    }
    
    // Database connection
    $host = 'localhost';
    $dbname = 'pengaduan_masyarakat';
    $username = 'root';
    $password = '';
    
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Check username exists
    $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ?");
    $stmt->execute([strtolower(trim($input['username']))]);
    
    if ($stmt->fetch()) {
        sendCleanResponse(false, 'Username sudah digunakan');
    }
    
    // Insert user
    $nama = trim($input['nama']);
    $username = strtolower(trim($input['username']));
    $hashedPassword = password_hash($input['password'], PASSWORD_DEFAULT);
    
    $stmt = $pdo->prepare("INSERT INTO users (nama, username, password, role, created_at) VALUES (?, ?, ?, 'user', NOW())");
    $result = $stmt->execute([$nama, $username, $hashedPassword]);
    
    if ($result) {
        $userId = $pdo->lastInsertId();
        sendCleanResponse(true, 'Pendaftaran berhasil!', [
            'user_id' => $userId,
            'username' => $username,
            'nama' => $nama
        ]);
    } else {
        sendCleanResponse(false, 'Gagal menyimpan data');
    }
    
} catch (PDOException $e) {
    sendCleanResponse(false, 'Database error: ' . $e->getMessage());
} catch (Exception $e) {
    sendCleanResponse(false, 'System error: ' . $e->getMessage());
}
?>
