<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <title>Test Clean Register</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input { padding: 10px; width: 300px; border: 1px solid #ddd; border-radius: 4px; }
        button { padding: 12px 20px; background: #007cba; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #005a8b; }
        button:disabled { background: #ccc; cursor: not-allowed; }
        .result { margin: 20px 0; padding: 15px; border-radius: 4px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .debug { background: #f8f9fa; border: 1px solid #dee2e6; color: #495057; font-family: monospace; white-space: pre-wrap; font-size: 12px; }
    </style>
</head>
<body>
    <h1>Test Clean Registration</h1>
    
    <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin-bottom: 20px; border-radius: 4px;">
        <strong>Clean Version:</strong> Form ini menggunakan register_clean.php yang dibuat khusus untuk menghindari error JSON parsing
    </div>
    
    <form id="cleanRegisterForm">
        <div class="form-group">
            <label for="nama">Nama Lengkap:</label>
            <input type="text" id="nama" name="nama" required>
        </div>
        
        <div class="form-group">
            <label for="username">Username:</label>
            <input type="text" id="username" name="username" required>
        </div>
        
        <div class="form-group">
            <label for="password">Password:</label>
            <input type="password" id="password" name="password" required>
        </div>
        
        <div class="form-group">
            <label for="confirmPassword">Konfirmasi Password:</label>
            <input type="password" id="confirmPassword" name="confirmPassword" required>
        </div>
        
        <button type="submit" id="submitBtn">Register</button>
    </form>
    
    <div id="result"></div>
    
    <h3>Test Links:</h3>
    <ul>
        <li><a href="debug_register_response.php">Debug Register Response</a></li>
        <li><a href="register.html">Original Register Form</a></li>
        <li><a href="view_errors.php">View Error Logs</a></li>
    </ul>

    <script>
        // Auto-fill form with test data
        document.getElementById('nama').value = 'Clean Test User ' + new Date().toLocaleTimeString();
        document.getElementById('username').value = 'cleanuser' + Date.now();
        document.getElementById('password').value = 'password123';
        document.getElementById('confirmPassword').value = 'password123';
        
        document.getElementById('cleanRegisterForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submitBtn');
            const originalText = submitBtn.textContent;
            
            // Disable button
            submitBtn.disabled = true;
            submitBtn.textContent = 'Registering...';
            
            const formData = {
                nama: document.getElementById('nama').value,
                username: document.getElementById('username').value,
                password: document.getElementById('password').value,
                confirmPassword: document.getElementById('confirmPassword').value
            };
            
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="debug">Sending request to register_clean.php...</div>';
            
            console.log('Sending data:', formData);
            
            fetch('register_clean.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            })
            .then(response => {
                console.log('Response status:', response.status);
                console.log('Response headers:', [...response.headers.entries()]);
                
                // Check content type
                const contentType = response.headers.get('content-type');
                console.log('Content-Type:', contentType);
                
                return response.text();
            })
            .then(text => {
                console.log('Raw response length:', text.length);
                console.log('Raw response:', text);
                
                // Show raw response first
                resultDiv.innerHTML = `
                    <div class="debug">
                        <strong>Raw Response (${text.length} chars):</strong><br>
                        ${text.replace(/</g, '&lt;').replace(/>/g, '&gt;')}
                    </div>
                `;
                
                // Try to parse JSON
                try {
                    const data = JSON.parse(text);
                    
                    if (data.success) {
                        resultDiv.innerHTML += `
                            <div class="success">
                                <strong>✓ Registration Success:</strong> ${data.message}
                            </div>
                        `;
                        
                        // Auto-generate new test data for next test
                        document.getElementById('nama').value = 'Clean Test User ' + new Date().toLocaleTimeString();
                        document.getElementById('username').value = 'cleanuser' + Date.now();
                        
                    } else {
                        resultDiv.innerHTML += `
                            <div class="error">
                                <strong>✗ Registration Failed:</strong> ${data.message}
                            </div>
                        `;
                    }
                    
                    resultDiv.innerHTML += `
                        <div class="debug">
                            <strong>Parsed JSON:</strong><br>
                            ${JSON.stringify(data, null, 2)}
                        </div>
                    `;
                    
                } catch (e) {
                    console.error('JSON parse error:', e);
                    
                    resultDiv.innerHTML += `
                        <div class="error">
                            <strong>✗ JSON Parse Error:</strong> ${e.message}
                        </div>
                    `;
                    
                    // Check for common issues
                    if (text.includes('<?php')) {
                        resultDiv.innerHTML += `
                            <div class="error">
                                <strong>Issue:</strong> Response contains PHP code - possible syntax error
                            </div>
                        `;
                    }
                    
                    if (text.includes('Fatal error') || text.includes('Parse error')) {
                        resultDiv.innerHTML += `
                            <div class="error">
                                <strong>Issue:</strong> PHP fatal/parse error detected
                            </div>
                        `;
                    }
                    
                    if (text.includes('Warning') || text.includes('Notice')) {
                        resultDiv.innerHTML += `
                            <div class="error">
                                <strong>Issue:</strong> PHP warning/notice detected
                            </div>
                        `;
                    }
                    
                    if (text.trim() === '') {
                        resultDiv.innerHTML += `
                            <div class="error">
                                <strong>Issue:</strong> Empty response from server
                            </div>
                        `;
                    }
                }
            })
            .catch(error => {
                console.error('Fetch error:', error);
                resultDiv.innerHTML = `
                    <div class="error">
                        <strong>✗ Network Error:</strong> ${error.message}
                    </div>
                `;
            })
            .finally(() => {
                // Re-enable button
                submitBtn.disabled = false;
                submitBtn.textContent = originalText;
            });
        });
    </script>
</body>
</html>
