<?php
// setup_database.php - Script untuk membuat database dan tabel
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Setup Database Pengaduan Masyarakat</h2>";

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';

try {
    // Connect to MySQL server (without database)
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p>✓ Koneksi ke MySQL server berhasil</p>";
    
    // Create database
    $sql = "CREATE DATABASE IF NOT EXISTS pengaduan_masyarakat CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
    $pdo->exec($sql);
    echo "<p>✓ Database 'pengaduan_masyarakat' berhasil dibuat</p>";
    
    // Connect to the new database
    $pdo = new PDO("mysql:host=$host;dbname=pengaduan_masyarakat", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Create users table
    $sql = "CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        nama VARCHAR(255) NOT NULL,
        username VARCHAR(50) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        role ENUM('user', 'petugas', 'admin') DEFAULT 'user',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    $pdo->exec($sql);
    echo "<p>✓ Tabel 'users' berhasil dibuat</p>";
    
    // Create reports table
    $sql = "CREATE TABLE IF NOT EXISTS reports (
        id INT AUTO_INCREMENT PRIMARY KEY,
        report_id VARCHAR(10) NOT NULL UNIQUE,
        pelapor_id INT NOT NULL,
        judul VARCHAR(255) NOT NULL,
        isi TEXT NOT NULL,
        foto VARCHAR(255),
        status ENUM('menunggu', 'diproses', 'selesai', 'ditolak') DEFAULT 'menunggu',
        tanggal DATE NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (pelapor_id) REFERENCES users(id) ON DELETE CASCADE
    )";
    $pdo->exec($sql);
    echo "<p>✓ Tabel 'reports' berhasil dibuat</p>";
    
    // Create responses table
    $sql = "CREATE TABLE IF NOT EXISTS responses (
        id INT AUTO_INCREMENT PRIMARY KEY,
        report_id INT NOT NULL,
        petugas_id INT NOT NULL,
        tanggapan TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (report_id) REFERENCES reports(id) ON DELETE CASCADE,
        FOREIGN KEY (petugas_id) REFERENCES users(id) ON DELETE CASCADE
    )";
    $pdo->exec($sql);
    echo "<p>✓ Tabel 'responses' berhasil dibuat</p>";
    
    // Insert default admin user
    $sql = "INSERT IGNORE INTO users (nama, username, password, role) VALUES 
            ('Administrator', 'admin', ?, 'admin')";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([password_hash('password', PASSWORD_DEFAULT)]);
    echo "<p>✓ User admin default berhasil dibuat (username: admin, password: password)</p>";
    
    // Insert default petugas user  
    $sql = "INSERT IGNORE INTO users (nama, username, password, role) VALUES 
            ('Petugas Layanan', 'petugas', ?, 'petugas')";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([password_hash('password', PASSWORD_DEFAULT)]);
    echo "<p>✓ User petugas default berhasil dibuat (username: petugas, password: password)</p>";
    
    // Create indexes
    $indexes = [
        "CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)",
        "CREATE INDEX IF NOT EXISTS idx_reports_pelapor ON reports(pelapor_id)",
        "CREATE INDEX IF NOT EXISTS idx_reports_status ON reports(status)",
        "CREATE INDEX IF NOT EXISTS idx_reports_tanggal ON reports(tanggal)",
        "CREATE INDEX IF NOT EXISTS idx_responses_report ON responses(report_id)"
    ];
    
    foreach ($indexes as $index) {
        $pdo->exec($index);
    }
    echo "<p>✓ Index database berhasil dibuat</p>";
    
    echo "<h3 style='color: green;'>✓ Setup database berhasil!</h3>";
    echo "<p>Sekarang Anda dapat melihat database 'pengaduan_masyarakat' di phpMyAdmin</p>";
    echo "<p><strong>Catatan:</strong> Password default untuk admin dan petugas adalah 'password'. Silakan ubah setelah login.</p>";
    
} catch(PDOException $e) {
    echo "<h3 style='color: red;'>✗ Error: " . $e->getMessage() . "</h3>";
    echo "<p>Pastikan:</p>";
    echo "<ul>";
    echo "<li>XAMPP/MySQL server sudah berjalan</li>";
    echo "<li>Username dan password MySQL benar</li>";
    echo "<li>Port MySQL (biasanya 3306) tidak diblokir</li>";
    echo "</ul>";
}
?>
