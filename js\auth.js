// Admin credentials (hardcoded)
const adminCredentials = {
  username: "admin",
  password: "admin123",
  role: "admin",
  nama: "Administrator"
};

// Petugas credentials (hardcoded)
const petugasCredentials = {
  username: "petugas",
  password: "petugas123",
  role: "petugas",
  nama: "Petugas Layanan"
};

// Admin registration code (for security)
const ADMIN_REGISTRATION_CODE = "admin113 ";

// Store admin in localStorage if not already present
if (!localStorage.getItem('admin')) {
  localStorage.setItem('admin', JSON.stringify(adminCredentials));
}

// Store petugas in localStorage if not already present
if (!localStorage.getItem('petugas')) {
  localStorage.setItem('petugas', JSON.stringify(petugasCredentials));
}

// Handle login form submission
function handleLogin(e) {
  e.preventDefault();

  const username = document.getElementById('username').value;
  const password = document.getElementById('password').value;

  // Check if user exists
  const userData = localStorage.getItem(username);

  if (!userData) {
    alert('Username atau password salah!');
    return;
  }

  const user = JSON.parse(userData);

  // Validate password
  if (user.password !== password) {
    alert('Username atau password salah!');
    return;
  }

  // Set logged in user
  localStorage.setItem('loggedInUser', username);

  // Redirect based on role
  if (user.role === 'admin' || user.role === 'petugas') {
    window.location.href = 'dashboard.html';
  } else {
    window.location.href = 'user-dashboard.html';
  }
}

// Handle register form submission
function handleRegister(e) {
  e.preventDefault();

  const nama = document.getElementById('nama').value;
  const username = document.getElementById('username').value;
  const password = document.getElementById('password').value;
  const confirmPassword = document.getElementById('confirmPassword').value;

  // Validate passwords match
  if (password !== confirmPassword) {
    alert('Password tidak cocok!');
    return;
  }

  // Check if username already exists
  if (localStorage.getItem(username)) {
    alert('Username sudah digunakan!');
    return;
  }

  // Store user data
  const userData = {
    nama: nama,
    username: username,
    password: password,
    role: 'user'
  };

  localStorage.setItem(username, JSON.stringify(userData));
  alert('Pendaftaran berhasil! Silahkan login.');
  window.location.href = 'login.html';
}

// Handle admin register form submission
function handleAdminRegister(e) {
  e.preventDefault();

  const nama = document.getElementById('nama').value;
  const username = document.getElementById('username').value;
  const password = document.getElementById('password').value;
  const confirmPassword = document.getElementById('confirmPassword').value;
  const adminCode = document.getElementById('adminCode').value;

  // Validate passwords match
  if (password !== confirmPassword) {
    alert('Password tidak cocok!');
    return;
  }

  // Validate admin code
  if (adminCode !== ADMIN_REGISTRATION_CODE) {
    alert('Kode admin tidak valid!');
    return;
  }

  // Check if username already exists
  if (localStorage.getItem(username)) {
    alert('Username sudah digunakan!');
    return;
  }

  // Store admin data
  const userData = {
    nama: nama,
    username: username,
    password: password,
    role: 'admin'
  };

  localStorage.setItem(username, JSON.stringify(userData));
  alert('Pendaftaran admin berhasil! Silahkan login.');
  window.location.href = 'login.html';
}

// Handle petugas register form submission
function handlePetugasRegister(e) {
  e.preventDefault();

  // Check if current user is admin
  if (!isAdmin()) {
    alert('Hanya admin yang dapat mendaftarkan petugas!');
    window.location.href = 'dashboard.html';
    return;
  }

  const nama = document.getElementById('nama').value;
  const username = document.getElementById('username').value;
  const password = document.getElementById('password').value;
  const confirmPassword = document.getElementById('confirmPassword').value;

  // Validate passwords match
  if (password !== confirmPassword) {
    alert('Password tidak cocok!');
    return;
  }

  // Check if username already exists
  if (localStorage.getItem(username)) {
    alert('Username sudah digunakan!');
    return;
  }

  // Store petugas data
  const userData = {
    nama: nama,
    username: username,
    password: password,
    role: 'petugas'
  };

  localStorage.setItem(username, JSON.stringify(userData));
  alert('Pendaftaran petugas berhasil!');
  window.location.href = 'dashboard.html';
}

// Initialize event listeners when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
  // Add event listener for login form if it exists
  const loginForm = document.getElementById('loginForm');
  if (loginForm) {
    loginForm.addEventListener('submit', handleLogin);
  }

  // Add event listener for regular user register form if it exists
  const registerForm = document.getElementById('registerForm');
  if (registerForm) {
    registerForm.addEventListener('submit', handleRegister);
  }

  // Add event listener for admin register form if it exists
  const adminRegisterForm = document.getElementById('adminRegisterForm');
  if (adminRegisterForm) {
    adminRegisterForm.addEventListener('submit', handleAdminRegister);
  }

  // Add event listener for petugas register form if it exists
  const petugasRegisterForm = document.getElementById('petugasRegisterForm');
  if (petugasRegisterForm) {
    petugasRegisterForm.addEventListener('submit', handlePetugasRegister);
  }
});