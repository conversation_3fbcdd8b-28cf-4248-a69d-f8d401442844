<?php
// debug_login.php - Debug login process step by step
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Debug Login Process</h2>";

// Include database configuration
require_once 'config/database.php';

// Test specific username and password
$testUsername = isset($_GET['username']) ? $_GET['username'] : '';
$testPassword = isset($_GET['password']) ? $_GET['password'] : '';

if (!empty($testUsername) && !empty($testPassword)) {
    echo "<h3>Testing Login for: $testUsername</h3>";
    
    try {
        $pdo = getDatabaseConnection();
        echo "<p style='color: green;'>✓ Database connection successful</p>";
        
        // Step 1: Check if user exists
        echo "<h4>Step 1: Check if user exists</h4>";
        $stmt = $pdo->prepare("SELECT id, nama, username, password, role FROM users WHERE username = ?");
        $stmt->execute([strtolower(trim($testUsername))]);
        $user = $stmt->fetch();
        
        if (!$user) {
            echo "<p style='color: red;'>✗ User not found with username: " . strtolower(trim($testUsername)) . "</p>";
            
            // Show similar usernames
            $stmt = $pdo->prepare("SELECT username FROM users WHERE username LIKE ?");
            $stmt->execute(['%' . $testUsername . '%']);
            $similar = $stmt->fetchAll();
            
            if (!empty($similar)) {
                echo "<p>Similar usernames found:</p>";
                echo "<ul>";
                foreach ($similar as $sim) {
                    echo "<li>{$sim['username']}</li>";
                }
                echo "</ul>";
            }
            
            // Show all usernames
            $stmt = $pdo->query("SELECT username FROM users ORDER BY username");
            $allUsers = $stmt->fetchAll();
            echo "<p>All available usernames:</p>";
            echo "<ul>";
            foreach ($allUsers as $u) {
                echo "<li>{$u['username']}</li>";
            }
            echo "</ul>";
            
        } else {
            echo "<p style='color: green;'>✓ User found:</p>";
            echo "<ul>";
            echo "<li><strong>ID:</strong> {$user['id']}</li>";
            echo "<li><strong>Nama:</strong> {$user['nama']}</li>";
            echo "<li><strong>Username:</strong> {$user['username']}</li>";
            echo "<li><strong>Role:</strong> {$user['role']}</li>";
            echo "<li><strong>Password Hash:</strong> " . substr($user['password'], 0, 50) . "...</li>";
            echo "</ul>";
            
            // Step 2: Test password verification
            echo "<h4>Step 2: Password Verification</h4>";
            echo "<p><strong>Input Password:</strong> '$testPassword'</p>";
            echo "<p><strong>Stored Hash:</strong> {$user['password']}</p>";
            
            $isValid = password_verify($testPassword, $user['password']);
            
            if ($isValid) {
                echo "<p style='color: green;'>✓ Password verification successful!</p>";
                echo "<p>Login should work with these credentials.</p>";
            } else {
                echo "<p style='color: red;'>✗ Password verification failed!</p>";
                
                // Test common passwords
                echo "<h5>Testing Common Passwords:</h5>";
                $commonPasswords = ['password', 'password123', '123456', 'admin', $testUsername];
                
                foreach ($commonPasswords as $testPass) {
                    $test = password_verify($testPass, $user['password']);
                    echo "<p>Password '$testPass': " . ($test ? "<span style='color: green;'>✓ MATCH</span>" : "<span style='color: red;'>✗ No match</span>") . "</p>";
                }
                
                // Check if password is plain text (old format)
                if ($user['password'] === $testPassword) {
                    echo "<p style='color: orange;'>⚠ Password stored as plain text! This is insecure.</p>";
                    echo "<p>Updating to hashed password...</p>";
                    
                    $hashedPassword = password_hash($testPassword, PASSWORD_DEFAULT);
                    $updateStmt = $pdo->prepare("UPDATE users SET password = ? WHERE id = ?");
                    $updateStmt->execute([$hashedPassword, $user['id']]);
                    
                    echo "<p style='color: green;'>✓ Password updated to hashed format</p>";
                }
            }
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    }
    
} else {
    // Show form to test login
    echo "<h3>Test Login Credentials</h3>";
    echo "<form method='GET' style='border: 1px solid #ddd; padding: 20px; background: #f9f9f9; max-width: 400px;'>";
    echo "<div style='margin-bottom: 15px;'>";
    echo "<label for='username' style='display: block; margin-bottom: 5px; font-weight: bold;'>Username:</label>";
    echo "<input type='text' id='username' name='username' required style='width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px;'>";
    echo "</div>";
    echo "<div style='margin-bottom: 15px;'>";
    echo "<label for='password' style='display: block; margin-bottom: 5px; font-weight: bold;'>Password:</label>";
    echo "<input type='text' id='password' name='password' required style='width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px;'>";
    echo "</div>";
    echo "<button type='submit' style='padding: 10px 20px; background: #007cba; color: white; border: none; border-radius: 4px; cursor: pointer;'>Debug Login</button>";
    echo "</form>";
}

// Show all users for reference
echo "<hr>";
echo "<h3>All Users in Database:</h3>";

try {
    $pdo = getDatabaseConnection();
    $stmt = $pdo->query("SELECT id, nama, username, role, created_at FROM users ORDER BY created_at DESC");
    $users = $stmt->fetchAll();
    
    if (!empty($users)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f2f2f2;'>";
        echo "<th style='padding: 8px;'>ID</th>";
        echo "<th style='padding: 8px;'>Nama</th>";
        echo "<th style='padding: 8px;'>Username</th>";
        echo "<th style='padding: 8px;'>Role</th>";
        echo "<th style='padding: 8px;'>Created At</th>";
        echo "<th style='padding: 8px;'>Debug</th>";
        echo "</tr>";
        
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>{$user['id']}</td>";
            echo "<td style='padding: 8px;'>{$user['nama']}</td>";
            echo "<td style='padding: 8px;'>{$user['username']}</td>";
            echo "<td style='padding: 8px;'>{$user['role']}</td>";
            echo "<td style='padding: 8px;'>{$user['created_at']}</td>";
            echo "<td style='padding: 8px;'>";
            echo "<a href='?username={$user['username']}&password=password123' style='color: #007cba; text-decoration: none; margin-right: 5px;'>Test pw123</a>";
            echo "<a href='?username={$user['username']}&password=password' style='color: #007cba; text-decoration: none;'>Test pw</a>";
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No users found</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h3>Quick Tests:</h3>";
echo "<ul>";
echo "<li><a href='?username=admin&password=password'>Test admin/password</a></li>";
echo "<li><a href='?username=petugas&password=password'>Test petugas/password</a></li>";
echo "</ul>";

echo "<h3>Links:</h3>";
echo "<ul>";
echo "<li><a href='check_passwords.php'>Check Password Hashes</a></li>";
echo "<li><a href='test_login.php'>Test Login Form</a></li>";
echo "<li><a href='login.html'>Login Page</a></li>";
echo "<li><a href='register.html'>Register New User</a></li>";
echo "</ul>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; border: 1px solid #ddd; }
th { background-color: #f2f2f2; }
a { color: #007cba; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
