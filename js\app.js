// Aplikasi Pengaduan Masyarakat - Main JavaScript File

// Initialize reports in localStorage if not exists
if (!localStorage.getItem('reports')) {
  localStorage.setItem('reports', JSON.stringify([]));
}

// Get all reports from localStorage
function getAllReports() {
  return JSON.parse(localStorage.getItem('reports') || '[]');
}

// Get reports by user
function getUserReports(username) {
  const reports = getAllReports();
  return reports.filter(report => report.pelapor === username);
}

// Get report by ID
function getReportById(id) {
  const reports = getAllReports();
  return reports.find(report => report.id === id);
}

// Add new report
function addReport(report) {
  const reports = getAllReports();
  const newReport = {
    ...report,
    id: generateReportId(),
    tanggal: new Date().toISOString().split('T')[0],
    status: "menunggu"
  };

  reports.push(newReport);
  localStorage.setItem('reports', JSON.stringify(reports));
  return newReport;
}

// Update report status
function updateReportStatus(id, status) {
  const reports = getAllReports();
  const index = reports.findIndex(report => report.id === id);

  if (index !== -1) {
    reports[index].status = status;
    localStorage.setItem('reports', JSON.stringify(reports));
    return true;
  }

  return false;
}

// Add tanggapan to report
function addTanggapan(reportId, tanggapan, status) {
  const reports = getAllReports();
  const index = reports.findIndex(report => report.id === reportId);

  if (index !== -1) {
    if (!reports[index].tanggapan) {
      reports[index].tanggapan = [];
    }

    reports[index].tanggapan.push({
      isi: tanggapan,
      tanggal: new Date().toISOString().split('T')[0],
      admin: localStorage.getItem('loggedInUser')
    });

    reports[index].status = status;
    localStorage.setItem('reports', JSON.stringify(reports));
    return true;
  }

  return false;
}

// Generate unique report ID
function generateReportId() {
  const reports = getAllReports();
  if (reports.length === 0) {
    return "001";
  }

  const lastId = reports[reports.length - 1].id;
  const nextId = parseInt(lastId) + 1;
  return nextId.toString().padStart(3, '0');
}

// Check if user is logged in
function isLoggedIn() {
  return localStorage.getItem('loggedInUser') !== null;
}

// Check if current user is admin
function isAdmin() {
  const username = localStorage.getItem('loggedInUser');
  if (!username) return false;

  const userData = JSON.parse(localStorage.getItem(username) || '{}');
  return userData.role === 'admin';
}

// Check if current user is petugas
function isPetugas() {
  const username = localStorage.getItem('loggedInUser');
  if (!username) return false;

  const userData = JSON.parse(localStorage.getItem(username) || '{}');
  return userData.role === 'petugas';
}

// Check if current user is staff (admin or petugas)
function isStaff() {
  return isAdmin() || isPetugas();
}

// Get current user data
function getCurrentUser() {
  const username = localStorage.getItem('loggedInUser');
  if (!username) return null;

  return JSON.parse(localStorage.getItem(username) || '{}');
}

// Get user's full name from username
function getUserFullName(username) {
  if (!username) return '';

  const userData = JSON.parse(localStorage.getItem(username) || '{}');
  return userData.nama || username; // Return nama if exists, otherwise return username
}

// Logout user
function logout() {
  localStorage.removeItem('loggedInUser');
  window.location.href = 'index.html';
}

// Convert base64 to file for preview
function base64ToFile(dataurl, filename) {
  const arr = dataurl.split(',');
  const mime = arr[0].match(/:(.*?);/)[1];
  const bstr = atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);

  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }

  return new File([u8arr], filename, { type: mime });
}

// Format date to Indonesian format
function formatDate(dateString) {
  const options = { year: 'numeric', month: 'long', day: 'numeric' };
  return new Date(dateString).toLocaleDateString('id-ID', options);
}

// Initialize navigation based on login status
function initNavigation() {
  const navContainer = document.getElementById('navLinks');
  if (!navContainer) return;

  // Add home button to all pages
  let navHTML = `<a href="index.html" class="mr-4 hover:bg-blue-700 px-3 py-1 rounded transition duration-200 flex items-center"><i class="fas fa-home mr-1"></i> Home</a>`;

  if (isLoggedIn()) {
    const user = getCurrentUser();

    if (isAdmin()) {
      // Admin navigation
      navHTML += `
        <a href="dashboard.html" class="mr-4 hover:bg-blue-700 px-3 py-1 rounded transition duration-200 flex items-center"><i class="fas fa-tachometer-alt mr-1"></i> Dashboard</a>
        <a href="laporan-admin.html" class="mr-4 hover:bg-blue-700 px-3 py-1 rounded transition duration-200 flex items-center"><i class="fas fa-file-alt mr-1"></i> Generate Laporan</a>
        <div class="flex items-center bg-blue-700 px-3 py-1 rounded mr-4">
          <i class="fas fa-user-shield mr-2"></i>
          <span class="font-medium">${user.nama} <span class="text-blue-200">(Admin)</span></span>
        </div>
        <button onclick="logout()" class="hover:bg-blue-700 px-3 py-1 rounded transition duration-200 flex items-center"><i class="fas fa-sign-out-alt mr-1"></i> Logout</button>
      `;
    } else if (isPetugas()) {
      // Petugas navigation
      navHTML += `
        <a href="dashboard.html" class="mr-4 hover:bg-blue-700 px-3 py-1 rounded transition duration-200 flex items-center"><i class="fas fa-tachometer-alt mr-1"></i> Dashboard</a>
        <div class="flex items-center bg-blue-700 px-3 py-1 rounded mr-4">
          <i class="fas fa-user-tie mr-2"></i>
          <span class="font-medium">${user.nama} <span class="text-blue-200">(Petugas)</span></span>
        </div>
        <button onclick="logout()" class="hover:bg-blue-700 px-3 py-1 rounded transition duration-200 flex items-center"><i class="fas fa-sign-out-alt mr-1"></i> Logout</button>
      `;
    } else {
      // Regular user navigation
      navHTML += `
        <a href="user-dashboard.html" class="mr-4 hover:bg-blue-700 px-3 py-1 rounded transition duration-200 flex items-center"><i class="fas fa-tachometer-alt mr-1"></i> Dashboard</a>
        <a href="laporan.html" class="mr-4 hover:bg-blue-700 px-3 py-1 rounded transition duration-200 flex items-center"><i class="fas fa-edit mr-1"></i> Buat Laporan</a>
        <div class="flex items-center bg-blue-700 px-3 py-1 rounded mr-4">
          <i class="fas fa-user mr-2"></i>
          <span class="font-medium">${user.nama}</span>
        </div>
        <button onclick="logout()" class="hover:bg-blue-700 px-3 py-1 rounded transition duration-200 flex items-center"><i class="fas fa-sign-out-alt mr-1"></i> Logout</button>
      `;
    }
  } else {
    navHTML += `
      <a href="login.html" class="mr-4 hover:bg-blue-700 px-3 py-1 rounded transition duration-200 flex items-center"><i class="fas fa-sign-in-alt mr-1"></i> Login</a>
      <a href="register.html" class="hover:bg-blue-700 px-3 py-1 rounded transition duration-200 flex items-center"><i class="fas fa-user-plus mr-1"></i> Register</a>
    `;
  }

  navContainer.innerHTML = navHTML;
}

// Initialize page on load
document.addEventListener('DOMContentLoaded', function() {
  // Initialize navigation
  initNavigation();
});
