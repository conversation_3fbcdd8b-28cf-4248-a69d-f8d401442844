<?php
// view_errors.php - View PHP error logs
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>PHP Error Logs</h2>";

// Check for error log files
$errorLogFiles = [
    'php_errors.log',
    'error.log',
    'errors.log',
    '../logs/error.log',
    '/tmp/php_errors.log'
];

$foundLogs = false;

foreach ($errorLogFiles as $logFile) {
    if (file_exists($logFile)) {
        $foundLogs = true;
        echo "<h3>Error Log: $logFile</h3>";
        
        $errors = file_get_contents($logFile);
        if (!empty($errors)) {
            // Get last 50 lines
            $lines = explode("\n", $errors);
            $lastLines = array_slice($lines, -50);
            
            echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd; max-height: 400px; overflow-y: scroll;'>";
            echo htmlspecialchars(implode("\n", $lastLines));
            echo "</pre>";
        } else {
            echo "<p>Log file kosong</p>";
        }
    }
}

if (!$foundLogs) {
    echo "<p>Tidak ada error log ditemukan</p>";
}

// Check PHP error log setting
echo "<h3>PHP Error Log Configuration:</h3>";
echo "<ul>";
echo "<li>log_errors: " . (ini_get('log_errors') ? 'ON' : 'OFF') . "</li>";
echo "<li>error_log: " . ini_get('error_log') . "</li>";
echo "<li>display_errors: " . (ini_get('display_errors') ? 'ON' : 'OFF') . "</li>";
echo "</ul>";

// Test database connection
echo "<h3>Test Database Connection:</h3>";
try {
    require_once 'config/database.php';
    $pdo = getDatabaseConnection();
    echo "<p style='color: green;'>✓ Database connection successful</p>";
    
    // Test if users table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: green;'>✓ Users table exists</p>";
        
        // Test insert
        $testUsername = 'test_' . time();
        $stmt = $pdo->prepare("INSERT INTO users (nama, username, password, role) VALUES (?, ?, ?, 'user')");
        $result = $stmt->execute(['Test User', $testUsername, password_hash('test123', PASSWORD_DEFAULT)]);
        
        if ($result) {
            echo "<p style='color: green;'>✓ Test insert successful</p>";
            
            // Clean up test user
            $pdo->prepare("DELETE FROM users WHERE username = ?")->execute([$testUsername]);
            echo "<p style='color: green;'>✓ Test user cleaned up</p>";
        } else {
            echo "<p style='color: red;'>✗ Test insert failed</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ Users table does not exist</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Database error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h3>Manual Registration Test:</h3>";
echo "<form method='POST' action='register.php' style='border: 1px solid #ddd; padding: 20px; background: #f9f9f9;'>";
echo "<p><strong>Test registrasi langsung ke register.php:</strong></p>";
echo "<input type='text' name='nama' placeholder='Nama Lengkap' value='Test User " . date('H:i:s') . "' required style='display: block; margin: 5px 0; padding: 8px; width: 200px;'>";
echo "<input type='text' name='username' placeholder='Username' value='testuser" . time() . "' required style='display: block; margin: 5px 0; padding: 8px; width: 200px;'>";
echo "<input type='password' name='password' placeholder='Password' value='password123' required style='display: block; margin: 5px 0; padding: 8px; width: 200px;'>";
echo "<input type='password' name='confirmPassword' placeholder='Confirm Password' value='password123' required style='display: block; margin: 5px 0; padding: 8px; width: 200px;'>";
echo "<button type='submit' style='margin: 10px 0; padding: 10px 20px; background: #007cba; color: white; border: none; cursor: pointer;'>Test Register</button>";
echo "</form>";

echo "<h3>Links:</h3>";
echo "<ul>";
echo "<li><a href='register.html'>Register Form</a></li>";
echo "<li><a href='debug_register.php'>Debug Register</a></li>";
echo "<li><a href='check_database.php'>Check Database</a></li>";
echo "<li><a href='setup_database.php'>Setup Database</a></li>";
echo "</ul>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
pre { font-size: 12px; }
a { color: #007cba; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
