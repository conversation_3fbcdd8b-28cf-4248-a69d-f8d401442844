<!DOCTYPE html>
<html lang="id">
<head>
  <meta charset="UTF-8">
  <title>Dashboard User - Pengaduan Masyarakat</title>

  <!-- Menggunakan Tailwind CSS untuk styling -->
  <script src="https://cdn.tailwindcss.com"></script>

  <!-- Font Awesome untuk ikon -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

  <!-- view untuk mobile perangkat mobile -->
  <meta name="viewport" content="width=device-width, initial-scale=1.0">

  <style>
    body {
      font-family: 'Poppins', sans-serif;
    }

    .dashboard-bg {
      background-color: #f3f4f6;
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%232563eb' fill-opacity='0.05'%3E%3Cpath opacity='.5' d='M96 95h4v1h-4v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9zm-1 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9z'/%3E%3Cpath d='M6 5V0H5v5H0v1h5v94h1V6h94V5H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    }

    .gradient-text {
      background: linear-gradient(90deg, #3b82f6, #1e40af);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      color: transparent;
    }

    .card-hover {
      transition: all 0.3s ease;
    }

    .card-hover:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    .status-badge {
      display: inline-block;
      padding: 0.25rem 0.75rem;
      border-radius: 9999px;
      font-size: 0.75rem;
      font-weight: 600;
    }

    .status-menunggu {
      background-color: #fef3c7;
      color: #d97706;
    }

    .status-terverifikasi {
      background-color: #d1fae5;
      color: #059669;
    }

    .status-ditolak {
      background-color: #fee2e2;
      color: #dc2626;
    }

    .status-selesai {
      background-color: #dbeafe;
      color: #2563eb;
    }
  </style>
</head>
<body class="bg-gray-50 font-sans dashboard-bg">

  <!-- Navigasi user -->
  <nav class="bg-blue-600 text-white p-4 flex justify-between items-center shadow-md sticky top-0 z-50">
    <h1 class="text-xl font-bold">
      <i class="fas fa-tachometer-alt mr-2"></i>
      Dashboard Masyarakat
    </h1>
    <div id="navLinks" class="flex items-center">
      <!-- Navigation links will be inserted by JavaScript -->
    </div>
  </nav>

  <!-- Konten utama dashboard -->
  <main class="container mx-auto px-4 py-8">
    <!-- Header dengan statistik -->
    <div class="bg-white rounded-xl shadow-md p-6 mb-8">
      <div class="flex flex-col md:flex-row justify-between items-center">
        <div>
          <h2 class="text-3xl font-bold gradient-text mb-2">Laporan Saya</h2>
          <p class="text-gray-600" id="userWelcome">Selamat datang kembali!</p>
        </div>
        <a href="laporan.html" class="mt-4 md:mt-0 bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition duration-300 flex items-center font-medium">
          <i class="fas fa-plus-circle mr-2"></i> Buat Laporan Baru
        </a>
      </div>
    </div>

    <!-- Statistik Laporan -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8" id="reportStats">
      <!-- Stats will be inserted by JavaScript -->
    </div>

    <!-- Daftar laporan -->
    <div id="userReports" class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- User reports will be inserted by JavaScript -->
    </div>
  </main>

  <!-- JavaScript -->
  <script src="js/app.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Check if user is logged in, redirect if not
      if (!isLoggedIn()) {
        alert('Anda harus login terlebih dahulu.');
        window.location.href = 'login.html';
        return;
      }

      // Check if user is admin, redirect if yes
      if (isAdmin()) {
        window.location.href = 'dashboard.html';
        return;
      }

      // Get current user
      const username = localStorage.getItem('loggedInUser');
      const user = getCurrentUser();

      // Update welcome message
      document.getElementById('userWelcome').textContent = `Selamat datang kembali, ${user.nama}!`;

      // Get user reports
      const reports = getUserReports(username);
      const userReportsContainer = document.getElementById('userReports');
      const reportStatsContainer = document.getElementById('reportStats');

      // Calculate report statistics
      const totalReports = reports.length;
      const menungguReports = reports.filter(report => report.status === 'menunggu').length;
      const terverifikasiReports = reports.filter(report => report.status === 'terverifikasi').length;
      const ditolakReports = reports.filter(report => report.status === 'ditolak').length;
      const selesaiReports = reports.filter(report => report.status === 'selesai').length;

      // Display report statistics
      reportStatsContainer.innerHTML = `
        <div class="bg-white rounded-xl shadow-md p-6 card-hover">
          <div class="flex items-center">
            <div class="bg-blue-100 p-3 rounded-full mr-4">
              <i class="fas fa-file-alt text-blue-600 text-xl"></i>
            </div>
            <div>
              <p class="text-gray-600 text-sm">Total Laporan</p>
              <p class="text-2xl font-bold">${totalReports}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-xl shadow-md p-6 card-hover">
          <div class="flex items-center">
            <div class="bg-yellow-100 p-3 rounded-full mr-4">
              <i class="fas fa-clock text-yellow-600 text-xl"></i>
            </div>
            <div>
              <p class="text-gray-600 text-sm">Menunggu</p>
              <p class="text-2xl font-bold">${menungguReports}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-xl shadow-md p-6 card-hover">
          <div class="flex items-center">
            <div class="bg-green-100 p-3 rounded-full mr-4">
              <i class="fas fa-check-circle text-green-600 text-xl"></i>
            </div>
            <div>
              <p class="text-gray-600 text-sm">Terverifikasi</p>
              <p class="text-2xl font-bold">${terverifikasiReports}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-xl shadow-md p-6 card-hover">
          <div class="flex items-center">
            <div class="bg-blue-100 p-3 rounded-full mr-4">
              <i class="fas fa-check-double text-blue-600 text-xl"></i>
            </div>
            <div>
              <p class="text-gray-600 text-sm">Selesai</p>
              <p class="text-2xl font-bold">${selesaiReports}</p>
            </div>
          </div>
        </div>
      `;

      // Display user reports
      if (reports.length > 0) {
        let reportsHTML = '';

        reports.forEach(report => {
          // Determine status class and text
          let statusClass = 'status-menunggu';
          let statusText = 'Menunggu Verifikasi';
          let statusIcon = 'clock';

          if (report.status === 'terverifikasi') {
            statusClass = 'status-terverifikasi';
            statusText = 'Terverifikasi';
            statusIcon = 'check-circle';
          } else if (report.status === 'ditolak') {
            statusClass = 'status-ditolak';
            statusText = 'Ditolak';
            statusIcon = 'times-circle';
          } else if (report.status === 'selesai') {
            statusClass = 'status-selesai';
            statusText = 'Selesai';
            statusIcon = 'check-double';
          }

          // Get response count
          const responseCount = report.tanggapan ? report.tanggapan.length : 0;

          // Format date
          const reportDate = new Date(report.tanggal);
          const formattedDate = formatDate(report.tanggal);

          // Create report card
          reportsHTML += `
            <div class="bg-white p-6 rounded-xl shadow-md card-hover border border-gray-100">
              <div class="flex justify-between items-start mb-4">
                <span class="status-badge ${statusClass}">
                  <i class="fas fa-${statusIcon} mr-1"></i> ${statusText}
                </span>
                <span class="text-xs text-gray-500">${formattedDate}</span>
              </div>

              <h3 class="font-bold text-xl text-gray-800 mb-3">${report.judul}</h3>

              <div class="mb-4 text-gray-600 line-clamp-3">
                ${report.isi.substring(0, 150)}${report.isi.length > 150 ? '...' : ''}
              </div>

              ${report.tanggapan && report.tanggapan.length > 0 ? `
                <div class="mb-4 p-4 bg-blue-50 rounded-lg border border-blue-100">
                  <div class="flex items-center mb-2">
                    <i class="fas fa-comment-dots text-blue-600 mr-2"></i>
                    <p class="font-semibold text-blue-800">Tanggapan Terbaru:</p>
                  </div>
                  <p class="text-gray-700">${report.tanggapan[report.tanggapan.length - 1].isi.substring(0, 100)}${report.tanggapan[report.tanggapan.length - 1].isi.length > 100 ? '...' : ''}</p>
                  <p class="text-xs text-gray-500 mt-2">
                    <i class="far fa-calendar-alt mr-1"></i>
                    ${formatDate(report.tanggapan[report.tanggapan.length - 1].tanggal)}
                  </p>
                </div>
              ` : ''}

              <div class="flex justify-between items-center pt-3 border-t border-gray-100">
                <div class="flex items-center text-gray-500 text-sm">
                  <i class="fas fa-comment mr-1"></i>
                  <span>${responseCount} Tanggapan</span>
                </div>
                <a href="detail-laporan.html?id=${report.id}" class="text-blue-600 hover:text-blue-800 font-medium flex items-center">
                  Lihat Detail
                  <i class="fas fa-arrow-right ml-1 text-xs"></i>
                </a>
              </div>
            </div>
          `;
        });

        userReportsContainer.innerHTML = reportsHTML;
      } else {
        userReportsContainer.innerHTML = `
          <div class="col-span-1 md:col-span-2 text-center p-10 bg-white rounded-xl shadow-md border border-gray-100">
            <div class="mb-6">
              <img src="https://img.freepik.com/free-vector/no-data-concept-illustration_114360-536.jpg" alt="Belum ada laporan" class="w-64 h-64 mx-auto">
            </div>
            <h3 class="text-xl font-semibold text-gray-800 mb-2">Belum Ada Laporan</h3>
            <p class="text-gray-500 mb-6 max-w-md mx-auto">Anda belum memiliki laporan. Silahkan buat laporan baru untuk melaporkan keluhan atau pengaduan Anda.</p>
            <a href="laporan.html" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold transition duration-300 inline-flex items-center">
              <i class="fas fa-edit mr-2"></i> Buat Laporan Baru
            </a>
          </div>
        `;
      }
    });
  </script>
</body>
</html>
