<?php
// test_register_final.php - Test register.php yang sudah diperbaiki
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Test Register.php Final</h2>";

// Test data
$testData = [
    'nama' => 'User Test ' . date('H:i:s'),
    'username' => 'user' . time(),
    'password' => 'password123',
    'confirmPassword' => 'password123'
];

echo "<h3>Test Data:</h3>";
echo "<ul>";
foreach ($testData as $key => $value) {
    echo "<li><strong>$key:</strong> $value</li>";
}
echo "</ul>";

// Test 1: JSON POST
echo "<h3>Test 1: JSON POST ke register.php</h3>";

$jsonData = json_encode($testData);
$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => [
            'Content-Type: application/json',
            'Content-Length: ' . strlen($jsonData)
        ],
        'content' => $jsonData
    ]
]);

$url = 'http://localhost/pengaduan-masyarakat/register.php';
$response = file_get_contents($url, false, $context);

if ($response !== false) {
    echo "<p style='color: green;'>✓ Request berhasil dikirim</p>";
    echo "<h4>Response:</h4>";
    echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd;'>";
    echo htmlspecialchars($response);
    echo "</pre>";
    
    $data = json_decode($response, true);
    if ($data) {
        if ($data['success']) {
            echo "<p style='color: green;'>✓ Registrasi berhasil!</p>";
        } else {
            echo "<p style='color: red;'>✗ Registrasi gagal: " . $data['message'] . "</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ Response bukan JSON valid</p>";
    }
} else {
    echo "<p style='color: red;'>✗ Request gagal</p>";
}

// Test 2: Form POST
echo "<hr>";
echo "<h3>Test 2: Form POST ke register.php</h3>";

$postData = http_build_query($testData);
$context2 = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => [
            'Content-Type: application/x-www-form-urlencoded',
            'Content-Length: ' . strlen($postData)
        ],
        'content' => $postData
    ]
]);

// Update test data untuk test kedua
$testData['username'] = 'user' . (time() + 1);
$postData = http_build_query($testData);
$context2 = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => [
            'Content-Type: application/x-www-form-urlencoded',
            'Content-Length: ' . strlen($postData)
        ],
        'content' => $postData
    ]
]);

$response2 = file_get_contents($url, false, $context2);

if ($response2 !== false) {
    echo "<p style='color: green;'>✓ Form POST berhasil dikirim</p>";
    echo "<h4>Response:</h4>";
    echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd;'>";
    echo htmlspecialchars($response2);
    echo "</pre>";
    
    $data2 = json_decode($response2, true);
    if ($data2) {
        if ($data2['success']) {
            echo "<p style='color: green;'>✓ Form POST registrasi berhasil!</p>";
        } else {
            echo "<p style='color: red;'>✗ Form POST registrasi gagal: " . $data2['message'] . "</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ Response bukan JSON valid</p>";
    }
} else {
    echo "<p style='color: red;'>✗ Form POST gagal</p>";
}

// Check database
echo "<hr>";
echo "<h3>Verifikasi Database:</h3>";

try {
    require_once 'config/database.php';
    $pdo = getDatabaseConnection();
    
    $stmt = $pdo->query("SELECT id, nama, username, role, created_at FROM users ORDER BY created_at DESC LIMIT 5");
    $users = $stmt->fetchAll();
    
    echo "<h4>5 User Terbaru:</h4>";
    if (!empty($users)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f2f2f2;'>";
        echo "<th style='padding: 8px;'>ID</th>";
        echo "<th style='padding: 8px;'>Nama</th>";
        echo "<th style='padding: 8px;'>Username</th>";
        echo "<th style='padding: 8px;'>Role</th>";
        echo "<th style='padding: 8px;'>Created At</th>";
        echo "</tr>";
        
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>{$user['id']}</td>";
            echo "<td style='padding: 8px;'>{$user['nama']}</td>";
            echo "<td style='padding: 8px;'>{$user['username']}</td>";
            echo "<td style='padding: 8px;'>{$user['role']}</td>";
            echo "<td style='padding: 8px;'>{$user['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>Tidak ada user ditemukan</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error mengakses database: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h3>Kesimpulan:</h3>";
echo "<p>Jika kedua test di atas berhasil, maka register.php sudah berfungsi dengan baik.</p>";
echo "<p>Sekarang Anda bisa test dengan form asli di <a href='register.html'>register.html</a></p>";

echo "<h3>Links:</h3>";
echo "<ul>";
echo "<li><a href='register.html'>Register Form</a></li>";
echo "<li><a href='view_errors.php'>View Error Logs</a></li>";
echo "<li><a href='debug_register.php'>Debug Register</a></li>";
echo "<li><a href='http://localhost/phpmyadmin' target='_blank'>phpMyAdmin</a></li>";
echo "</ul>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; border: 1px solid #ddd; }
th { background-color: #f2f2f2; }
a { color: #007cba; text-decoration: none; }
a:hover { text-decoration: underline; }
pre { font-size: 12px; }
</style>
