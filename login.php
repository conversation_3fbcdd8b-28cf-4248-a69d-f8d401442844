<?php
// login.php - Handle user login for Pengaduan Masyarakat

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', 'php_errors.log');

// Set content type to JSON
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Include database configuration
require_once 'config/database.php';

// Start session
session_start();

// Response function
function sendResponse($success, $message, $data = null) {
    echo json_encode([
        'success' => $success,
        'message' => $message,
        'data' => $data
    ]);
    exit;
}

// Validate input function
function validateLoginInput($data) {
    $errors = [];
    
    if (empty($data['username'])) {
        $errors[] = 'Username harus diisi';
    }
    
    if (empty($data['password'])) {
        $errors[] = 'Password harus diisi';
    }
    
    return $errors;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendResponse(false, 'Method not allowed');
}

try {
    // Get input data
    $rawInput = file_get_contents('php://input');
    $input = json_decode($rawInput, true);

    // If JSON input is empty, try to get from POST data
    if (empty($input)) {
        $input = $_POST;
    }

    // Check if we have any input data
    if (empty($input)) {
        sendResponse(false, 'No input data received');
    }

    // Create database connection
    $pdo = getDatabaseConnection();
    
    // Check required fields first
    $required = ['username', 'password'];
    $missing = [];

    foreach ($required as $field) {
        if (empty($input[$field])) {
            $missing[] = $field;
        }
    }

    if (!empty($missing)) {
        sendResponse(false, 'Field yang harus diisi: ' . implode(', ', $missing));
    }

    // Validate input
    $errors = validateLoginInput($input);
    if (!empty($errors)) {
        sendResponse(false, implode(', ', $errors));
    }
    
    // Sanitize input
    $username = trim(strtolower($input['username']));
    $password = $input['password'];
    
    // Get user from database
    $stmt = $pdo->prepare("SELECT id, nama, username, password, role FROM users WHERE username = ?");
    $stmt->execute([$username]);
    $user = $stmt->fetch();

    // Log for debugging
    error_log("Login attempt for username: $username");
    error_log("User found: " . ($user ? "Yes" : "No"));

    if (!$user) {
        error_log("User not found in database");
        sendResponse(false, 'Username atau password salah!');
    }

    // Log password verification attempt
    error_log("Attempting password verification for user: " . $user['username']);
    error_log("Password hash starts with: " . substr($user['password'], 0, 20));

    // Verify password
    $passwordValid = password_verify($password, $user['password']);
    error_log("Password verification result: " . ($passwordValid ? "SUCCESS" : "FAILED"));

    if (!$passwordValid) {
        // Check if password is stored as plain text (for debugging)
        if ($user['password'] === $password) {
            error_log("Password stored as plain text - updating to hash");
            $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
            $updateStmt = $pdo->prepare("UPDATE users SET password = ? WHERE id = ?");
            $updateStmt->execute([$hashedPassword, $user['id']]);
            $passwordValid = true;
        } else {
            error_log("Password verification failed for user: " . $user['username']);
            sendResponse(false, 'Username atau password salah!');
        }
    }
    
    // Set session data
    $_SESSION['user_id'] = $user['id'];
    $_SESSION['username'] = $user['username'];
    $_SESSION['nama'] = $user['nama'];
    $_SESSION['role'] = $user['role'];
    $_SESSION['logged_in'] = true;
    
    // Determine redirect URL based on role
    $redirectUrl = 'user-dashboard.html';
    if ($user['role'] === 'admin' || $user['role'] === 'petugas') {
        $redirectUrl = 'dashboard.html';
    }
    
    sendResponse(true, 'Login berhasil!', [
        'user_id' => $user['id'],
        'username' => $user['username'],
        'nama' => $user['nama'],
        'role' => $user['role'],
        'redirect_url' => $redirectUrl
    ]);
    
} catch (PDOException $e) {
    error_log("Database error: " . $e->getMessage());
    sendResponse(false, 'Terjadi kesalahan sistem. Silakan coba lagi nanti.');
    
} catch (Exception $e) {
    error_log("General error: " . $e->getMessage());
    sendResponse(false, 'Terjadi kesalahan sistem. Silakan coba lagi nanti.');
}
?>
