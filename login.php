<?php
// login.php - Handle user login for Pengaduan Masyarakat

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', 'php_errors.log');

// Set content type to JSON
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Include database configuration
require_once 'config/database.php';

// Start session
session_start();

// Response function
function sendResponse($success, $message, $data = null) {
    echo json_encode([
        'success' => $success,
        'message' => $message,
        'data' => $data
    ]);
    exit;
}

// Validate input function
function validateLoginInput($data) {
    $errors = [];
    
    if (empty($data['username'])) {
        $errors[] = 'Username harus diisi';
    }
    
    if (empty($data['password'])) {
        $errors[] = 'Password harus diisi';
    }
    
    return $errors;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendResponse(false, 'Method not allowed');
}

try {
    // Get input data
    $rawInput = file_get_contents('php://input');
    $input = json_decode($rawInput, true);

    // If JSON input is empty, try to get from POST data
    if (empty($input)) {
        $input = $_POST;
    }

    // Check if we have any input data
    if (empty($input)) {
        sendResponse(false, 'No input data received');
    }

    // Create database connection
    $pdo = getDatabaseConnection();
    
    // Check required fields first
    $required = ['username', 'password'];
    $missing = [];

    foreach ($required as $field) {
        if (empty($input[$field])) {
            $missing[] = $field;
        }
    }

    if (!empty($missing)) {
        sendResponse(false, 'Field yang harus diisi: ' . implode(', ', $missing));
    }

    // Validate input
    $errors = validateLoginInput($input);
    if (!empty($errors)) {
        sendResponse(false, implode(', ', $errors));
    }
    
    // Sanitize input
    $username = trim(strtolower($input['username']));
    $password = $input['password'];
    
    // Get user from database
    $stmt = $pdo->prepare("SELECT id, nama, username, password, role FROM users WHERE username = ?");
    $stmt->execute([$username]);
    $user = $stmt->fetch();
    
    if (!$user) {
        sendResponse(false, 'Username atau password salah!');
    }
    
    // Verify password
    if (!password_verify($password, $user['password'])) {
        sendResponse(false, 'Username atau password salah!');
    }
    
    // Set session data
    $_SESSION['user_id'] = $user['id'];
    $_SESSION['username'] = $user['username'];
    $_SESSION['nama'] = $user['nama'];
    $_SESSION['role'] = $user['role'];
    $_SESSION['logged_in'] = true;
    
    // Determine redirect URL based on role
    $redirectUrl = 'user-dashboard.html';
    if ($user['role'] === 'admin' || $user['role'] === 'petugas') {
        $redirectUrl = 'dashboard.html';
    }
    
    sendResponse(true, 'Login berhasil!', [
        'user_id' => $user['id'],
        'username' => $user['username'],
        'nama' => $user['nama'],
        'role' => $user['role'],
        'redirect_url' => $redirectUrl
    ]);
    
} catch (PDOException $e) {
    error_log("Database error: " . $e->getMessage());
    sendResponse(false, 'Terjadi kesalahan sistem. Silakan coba lagi nanti.');
    
} catch (Exception $e) {
    error_log("General error: " . $e->getMessage());
    sendResponse(false, 'Terjadi kesalahan sistem. Silakan coba lagi nanti.');
}
?>
