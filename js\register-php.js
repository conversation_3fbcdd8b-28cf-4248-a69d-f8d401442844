// register-php.js - Handle registration with PHP backend

// Handle register form submission with PHP backend
function handleRegisterPHP(e) {
  e.preventDefault();

  const nama = document.getElementById('nama').value.trim();
  const username = document.getElementById('username').value.trim();
  const password = document.getElementById('password').value;
  const confirmPassword = document.getElementById('confirmPassword').value;

  // Basic client-side validation
  if (!nama || !username || !password || !confirmPassword) {
    alert('Semua field harus diisi!');
    return;
  }

  if (password !== confirmPassword) {
    alert('Password dan konfirmasi password tidak cocok!');
    return;
  }

  if (password.length < 6) {
    alert('Password minimal 6 karakter!');
    return;
  }

  if (username.length < 3) {
    alert('Username minimal 3 karakter!');
    return;
  }

  // Disable submit button to prevent double submission
  const submitButton = document.querySelector('button[type="submit"]');
  const originalText = submitButton.innerHTML;
  submitButton.disabled = true;
  submitButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Mendaftar...';

  // Prepare data for PHP backend
  const formData = {
    nama: nama,
    username: username,
    password: password,
    confirmPassword: confirmPassword
  };

  // Send data to PHP backend
  fetch('register.php', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(formData)
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      alert(data.message);
      // Redirect to login page
      window.location.href = 'login.html';
    } else {
      alert('Error: ' + data.message);
    }
  })
  .catch(error => {
    console.error('Error:', error);
    alert('Terjadi kesalahan sistem. Silakan coba lagi nanti.');
  })
  .finally(() => {
    // Re-enable submit button
    submitButton.disabled = false;
    submitButton.innerHTML = originalText;
  });
}

// Alternative function for form data submission (if JSON doesn't work)
function handleRegisterPHPFormData(e) {
  e.preventDefault();

  const nama = document.getElementById('nama').value.trim();
  const username = document.getElementById('username').value.trim();
  const password = document.getElementById('password').value;
  const confirmPassword = document.getElementById('confirmPassword').value;

  // Basic client-side validation
  if (!nama || !username || !password || !confirmPassword) {
    alert('Semua field harus diisi!');
    return;
  }

  if (password !== confirmPassword) {
    alert('Password dan konfirmasi password tidak cocok!');
    return;
  }

  // Disable submit button
  const submitButton = document.querySelector('button[type="submit"]');
  const originalText = submitButton.innerHTML;
  submitButton.disabled = true;
  submitButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Mendaftar...';

  // Create FormData object
  const formData = new FormData();
  formData.append('nama', nama);
  formData.append('username', username);
  formData.append('password', password);
  formData.append('confirmPassword', confirmPassword);

  // Send data to PHP backend
  fetch('register.php', {
    method: 'POST',
    body: formData
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      alert(data.message);
      // Redirect to login page
      window.location.href = 'login.html';
    } else {
      alert('Error: ' + data.message);
    }
  })
  .catch(error => {
    console.error('Error:', error);
    alert('Terjadi kesalahan sistem. Silakan coba lagi nanti.');
  })
  .finally(() => {
    // Re-enable submit button
    submitButton.disabled = false;
    submitButton.innerHTML = originalText;
  });
}

// Initialize PHP registration when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
  const registerForm = document.getElementById('registerForm');
  if (registerForm) {
    // Remove existing event listeners
    registerForm.removeEventListener('submit', handleRegister);
    
    // Add PHP backend event listener
    registerForm.addEventListener('submit', handleRegisterPHP);
  }
});

// Function to switch between localStorage and PHP backend
function switchToPhpBackend() {
  const registerForm = document.getElementById('registerForm');
  if (registerForm) {
    // Remove localStorage handler
    registerForm.removeEventListener('submit', handleRegister);
    
    // Add PHP handler
    registerForm.addEventListener('submit', handleRegisterPHP);
    
    console.log('Switched to PHP backend for registration');
  }
}

// Function to switch back to localStorage
function switchToLocalStorage() {
  const registerForm = document.getElementById('registerForm');
  if (registerForm) {
    // Remove PHP handler
    registerForm.removeEventListener('submit', handleRegisterPHP);
    
    // Add localStorage handler
    registerForm.addEventListener('submit', handleRegister);
    
    console.log('Switched to localStorage for registration');
  }
}
