<!DOCTYPE html>
<html lang="id">
<head>
  <meta charset="UTF-8">
  <title>Detail Laporan - Pen<PERSON>duan Masyarakat</title>

  <!-- Menggunakan Tailwind CSS untuk styling -->
  <script src="https://cdn.tailwindcss.com"></script>

  <!-- Font Awesome untuk ikon -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

  <!-- view untuk mobile perangkat mobile -->
  <meta name="viewport" content="width=device-width, initial-scale=1.0">

  <style>
    body {
      font-family: 'Poppins', sans-serif;
    }

    .detail-bg {
      background-color: #f3f4f6;
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%232563eb' fill-opacity='0.05'%3E%3Cpath opacity='.5' d='M96 95h4v1h-4v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9zm-1 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9z'/%3E%3Cpath d='M6 5V0H5v5H0v1h5v94h1V6h94V5H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    }

    .gradient-text {
      background: linear-gradient(90deg, #3b82f6, #1e40af);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      color: transparent;
    }

    .status-badge {
      display: inline-block;
      padding: 0.25rem 0.75rem;
      border-radius: 9999px;
      font-size: 0.75rem;
      font-weight: 600;
    }

    .status-menunggu {
      background-color: #fef3c7;
      color: #d97706;
    }

    .status-terverifikasi {
      background-color: #d1fae5;
      color: #059669;
    }

    .status-ditolak {
      background-color: #fee2e2;
      color: #dc2626;
    }

    .status-selesai {
      background-color: #dbeafe;
      color: #2563eb;
    }
  </style>
</head>
<body class="bg-gray-50 font-sans detail-bg">

  <!-- Navigasi atas -->
  <nav class="bg-blue-600 text-white p-4 flex justify-between items-center shadow-md sticky top-0 z-50">
    <h1 class="text-xl font-bold">
      <i class="fas fa-comments mr-2"></i>
      Aplikasi Pengaduan Masyarakat
    </h1>
    <div id="navLinks" class="flex items-center">
      <!-- Navigation links will be inserted by JavaScript -->
    </div>
  </nav>

  <!-- Konten utama -->
  <div class="container mx-auto px-4 py-8">
    <div class="max-w-4xl mx-auto bg-white p-6 rounded-xl shadow-md">
      <!-- Header dengan tombol kembali -->
      <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold gradient-text">Detail Laporan</h2>
        <button id="backButton" class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg transition duration-300 flex items-center">
          <i class="fas fa-arrow-left mr-2"></i> Kembali
        </button>
      </div>

      <!-- Detail laporan -->
      <div id="reportDetail" class="mb-8">
        <!-- Report details will be inserted by JavaScript -->
      </div>

      <!-- Tanggapan -->
      <div id="tanggapanContainer" class="mt-8 border-t border-gray-200 pt-6">
        <h3 class="text-xl font-semibold mb-4 flex items-center">
          <i class="fas fa-comments text-blue-600 mr-2"></i> Tanggapan
        </h3>
        <div id="tanggapanList" class="space-y-4">
          <!-- Tanggapan will be inserted by JavaScript -->
        </div>
      </div>

      <!-- Tombol tanggapan untuk admin -->
      <div id="adminActions" class="mt-8 hidden">
        <a id="tanggapanLink" href="#" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition duration-300 flex items-center justify-center w-full md:w-auto md:inline-flex">
          <i class="fas fa-reply mr-2"></i> Beri Tanggapan
        </a>
      </div>
    </div>
  </div>

  <!-- JavaScript -->
  <script src="js/app.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Get report ID from URL
      const urlParams = new URLSearchParams(window.location.search);
      const reportId = urlParams.get('id');

      if (!reportId) {
        alert('ID laporan tidak ditemukan.');
        window.location.href = 'index.html';
        return;
      }

      // Get report details
      const report = getReportById(reportId);

      if (!report) {
        alert('Laporan tidak ditemukan.');
        window.location.href = 'index.html';
        return;
      }

      // Set page title
      document.title = `${report.judul} - Pengaduan Masyarakat`;

      // Display report details
      const reportDetail = document.getElementById('reportDetail');

      // Determine status class, text, and icon
      let statusClass = 'status-menunggu';
      let statusText = 'Menunggu Verifikasi';
      let statusIcon = 'clock';

      if (report.status === 'terverifikasi') {
        statusClass = 'status-terverifikasi';
        statusText = 'Terverifikasi';
        statusIcon = 'check-circle';
      } else if (report.status === 'ditolak') {
        statusClass = 'status-ditolak';
        statusText = 'Ditolak';
        statusIcon = 'times-circle';
      } else if (report.status === 'selesai') {
        statusClass = 'status-selesai';
        statusText = 'Selesai';
        statusIcon = 'check-double';
      }

      // Get reporter's full name
      const pelaporFullName = getUserFullName(report.pelapor);

      // Format date
      const formattedDate = formatDate(report.tanggal);

      // Get response count
      const responseCount = report.tanggapan ? report.tanggapan.length : 0;

      // Create report detail HTML
      reportDetail.innerHTML = `
        <h2 class="text-2xl font-bold mb-4">${report.judul}</h2>

        <div class="flex flex-wrap items-center gap-4 mb-6">
          <span class="status-badge ${statusClass}">
            <i class="fas fa-${statusIcon} mr-1"></i> ${statusText}
          </span>

          <div class="flex items-center text-gray-600">
            <i class="far fa-calendar-alt mr-1"></i>
            <span>${formattedDate}</span>
          </div>

          <div class="flex items-center text-gray-600">
            <i class="fas fa-comment-dots mr-1"></i>
            <span>${responseCount} Tanggapan</span>
          </div>

          <div class="flex items-center text-gray-600">
            <i class="fas fa-hashtag mr-1"></i>
            <span>ID: ${report.id}</span>
          </div>
        </div>

        <div class="bg-blue-50 p-4 rounded-lg mb-6 flex items-start">
          <div class="bg-blue-100 w-10 h-10 rounded-full flex items-center justify-center mr-3 mt-1">
            <i class="fas fa-user text-blue-600"></i>
          </div>
          <div>
            <p class="font-medium text-gray-800">Pelapor:</p>
            <p class="text-gray-700">${pelaporFullName}</p>
          </div>
        </div>

        <div class="mb-6">
          <h3 class="text-lg font-semibold mb-2 text-gray-800">Deskripsi Laporan:</h3>
          <div class="bg-gray-50 p-4 rounded-lg border border-gray-100">
            <p class="whitespace-pre-line text-gray-700">${report.isi}</p>
          </div>
        </div>

        ${report.foto ? `
          <div class="mb-4">
            <h3 class="text-lg font-semibold mb-2 text-gray-800">Foto Pendukung:</h3>
            <div class="bg-gray-50 p-2 rounded-lg border border-gray-100 inline-block">
              <img src="${report.foto}" class="max-w-full max-h-96 rounded-lg" alt="Foto Laporan">
            </div>
          </div>
        ` : ''}
      `;

      // Display tanggapan
      const tanggapanList = document.getElementById('tanggapanList');

      if (report.tanggapan && report.tanggapan.length > 0) {
        let tanggapanHTML = '';

        report.tanggapan.forEach((tanggapan, index) => {
          // Get admin's full name
          const adminFullName = getUserFullName(tanggapan.admin);

          // Format date
          const tanggapanDate = formatDate(tanggapan.tanggal);

          // Determine if admin or petugas
          const isAdminUser = tanggapan.admin.startsWith('admin');
          const userIcon = isAdminUser ? 'user-shield' : 'user-tie';
          const userRole = isAdminUser ? 'Administrator' : 'Petugas';
          const bgColor = isAdminUser ? 'bg-blue-50' : 'bg-green-50';
          const borderColor = isAdminUser ? 'border-blue-100' : 'border-green-100';
          const avatarBg = isAdminUser ? 'bg-blue-100' : 'bg-green-100';
          const avatarColor = isAdminUser ? 'text-blue-600' : 'text-green-600';

          tanggapanHTML += `
            <div class="${bgColor} p-5 rounded-lg border ${borderColor}">
              <div class="flex items-start">
                <div class="${avatarBg} w-10 h-10 rounded-full flex items-center justify-center mr-3">
                  <i class="fas fa-${userIcon} ${avatarColor}"></i>
                </div>
                <div class="flex-1">
                  <div class="flex justify-between items-center mb-2">
                    <div>
                      <span class="font-medium text-gray-800">${adminFullName}</span>
                      <span class="text-xs text-gray-500 ml-2">${userRole}</span>
                    </div>
                    <div class="text-xs text-gray-500 flex items-center">
                      <i class="far fa-clock mr-1"></i> ${tanggapanDate}
                    </div>
                  </div>
                  <div class="whitespace-pre-line text-gray-700">${tanggapan.isi}</div>
                </div>
              </div>
            </div>
          `;
        });

        tanggapanList.innerHTML = tanggapanHTML;
      } else {
        tanggapanList.innerHTML = `
          <div class="text-center py-8">
            <div class="bg-gray-100 w-16 h-16 mx-auto rounded-full flex items-center justify-center mb-4">
              <i class="fas fa-comment-slash text-gray-400 text-xl"></i>
            </div>
            <p class="text-gray-500 mb-2">Belum ada tanggapan.</p>
            <p class="text-gray-400 text-sm">Tanggapan akan muncul di sini setelah petugas menanggapi laporan Anda.</p>
          </div>
        `;
      }

      // Show admin/petugas actions if user is staff
      if (isStaff()) {
        const adminActions = document.getElementById('adminActions');
        const tanggapanLink = document.getElementById('tanggapanLink');

        adminActions.classList.remove('hidden');
        tanggapanLink.href = `tanggapan.html?id=${reportId}`;

        // If user is petugas, hide actions for reports that are already completed
        if (isPetugas() && report.status === 'selesai') {
          adminActions.classList.add('hidden');
        }
      }

      // Handle back button
      const backButton = document.getElementById('backButton');

      backButton.addEventListener('click', function() {
        if (isStaff()) {
          window.location.href = 'dashboard.html';
        } else if (isLoggedIn() && getCurrentUser().username === report.pelapor) {
          window.location.href = 'user-dashboard.html';
        } else {
          window.location.href = 'index.html';
        }
      });
    });
  </script>
</body>
</html>
