<!DOCTYPE html>
<html lang="id">
<head>
  <meta charset="UTF-8">
  <title>Register - Pengaduan Masyarakat</title>

  <!-- Menggunakan Tailwind CSS untuk styling -->
  <script src="https://cdn.tailwindcss.com"></script>

  <!-- Font Awesome untuk ikon -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

  <!-- view untuk mobile perangkat mobile -->
  <meta name="viewport" content="width=device-width, initial-scale=1.0">

  <style>
    body {
      font-family: 'Poppins', sans-serif;
    }

    .register-bg {
      background-color: #f3f4f6;
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%232563eb' fill-opacity='0.05'%3E%3Cpath opacity='.5' d='M96 95h4v1h-4v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9zm-1 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9z'/%3E%3Cpath d='M6 5V0H5v5H0v1h5v94h1V6h94V5H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    }

    .gradient-text {
      background: linear-gradient(90deg, #3b82f6, #1e40af);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      color: transparent;
    }
  </style>
</head>
<body class="bg-gray-50 font-sans register-bg">
  <!-- Navigasi atas -->
  <nav class="bg-blue-600 text-white p-4 flex justify-between items-center shadow-md sticky top-0 z-50">
    <h1 class="text-xl font-bold">
      <i class="fas fa-comments mr-2"></i>
      Aplikasi Pengaduan Masyarakat
    </h1>
    <div id="navLinks" class="flex items-center">
      <!-- Navigation links will be inserted by JavaScript -->
    </div>
  </nav>

  <div class="container mx-auto px-4 py-8">
    <div class="max-w-md mx-auto bg-white p-8 rounded-xl shadow-md">
      <!-- Judul register -->
      <div class="text-center mb-8">
        <h2 class="text-3xl font-bold gradient-text">Daftar Akun</h2>
        <p class="text-gray-600 mt-2">Silahkan daftar untuk membuat akun laporan pengaduan</p>
      </div>

      <!-- Form pendaftaran -->
      <form id="registerForm" class="space-y-6">
        <!-- Input nama -->
        <div>
          <label for="nama" class="block text-gray-700 font-medium mb-2">Nama Lengkap</label>
          <input type="text" id="nama" name="nama" required
            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition"
            placeholder="Masukkan nama lengkap">
        </div>

        <!-- Input username -->
        <div>
          <label for="username" class="block text-gray-700 font-medium mb-2">Username</label>
          <input type="text" id="username" name="username" required
            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition"
            placeholder="Masukkan username">
        </div>

        <!-- Input password -->
        <div>
          <label for="password" class="block text-gray-700 font-medium mb-2">Password</label>
          <input type="password" id="password" name="password" required
            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition"
            placeholder="Masukkan password">
        </div>

        <!-- Input konfirmasi password -->
        <div>
          <label for="confirmPassword" class="block text-gray-700 font-medium mb-2">Konfirmasi Password</label>
          <input type="password" id="confirmPassword" name="confirmPassword" required
            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition"
            placeholder="Konfirmasi password">
        </div>

        <!-- Tombol submit -->
        <button type="submit"
          class="w-full bg-blue-600 text-white py-3 rounded-lg hover:bg-blue-700 transition duration-300 flex items-center justify-center font-medium">
          <i class="fas fa-user-plus mr-2"></i> Daftar
        </button>
      </form>

      <!-- Links -->
      <div class="mt-6 text-center">
        <p class="text-gray-600 mb-2">
          Sudah punya akun? <a href="login.html" class="text-blue-600 hover:text-blue-800 font-medium">Login di sini</a>
        </p>
        <p class="text-gray-600">
          <a href="admin-register.html" class="text-blue-600 hover:text-blue-800 font-medium">
            <i class="fas fa-user-shield mr-1"></i> Daftar sebagai Administrator
          </a>
        </p>
      </div>
    </div>
  </div>

  <!-- Memuat file JavaScript eksternal -->
  <script src="js/app.js"></script>
  <script src="js/register-php.js"></script>
</body>
</html>
