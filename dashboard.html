<!DOCTYPE html>
<html lang="id">
<head>
  <meta charset="UTF-8">
  <title>Dashboard Admin - Pengaduan Masyarakat</title>

  <!-- Menggunakan Tailwind CSS untuk styling -->
  <script src="https://cdn.tailwindcss.com"></script>

  <!-- Font Awesome untuk ikon -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

  <!-- view untuk mobile perangkat mobile -->
  <meta name="viewport" content="width=device-width, initial-scale=1.0">

  <style>
    body {
      font-family: 'Poppins', sans-serif;
    }

    .dashboard-bg {
      background-color: #f3f4f6;
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%232563eb' fill-opacity='0.05'%3E%3Cpath opacity='.5' d='M96 95h4v1h-4v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9zm-1 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9z'/%3E%3Cpath d='M6 5V0H5v5H0v1h5v94h1V6h94V5H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    }

    .gradient-text {
      background: linear-gradient(90deg, #3b82f6, #1e40af);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      color: transparent;
    }

    .status-badge {
      display: inline-block;
      padding: 0.25rem 0.75rem;
      border-radius: 9999px;
      font-size: 0.75rem;
      font-weight: 600;
    }

    .status-menunggu {
      background-color: #fef3c7;
      color: #d97706;
    }

    .status-terverifikasi {
      background-color: #d1fae5;
      color: #059669;
    }

    .status-ditolak {
      background-color: #fee2e2;
      color: #dc2626;
    }

    .status-selesai {
      background-color: #dbeafe;
      color: #2563eb;
    }

    .filter-button {
      transition: all 0.3s ease;
      border-radius: 0.5rem;
      padding: 0.5rem 1rem;
      font-weight: 500;
    }

    .filter-button:hover {
      transform: translateY(-2px);
    }

    .filter-button.active {
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }

    .table-container {
      border-radius: 0.75rem;
      overflow: hidden;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }

    .action-button {
      transition: all 0.3s ease;
    }

    .action-button:hover {
      transform: translateY(-2px);
    }
  </style>
</head>
<body class="bg-gray-50 font-sans dashboard-bg">

  <!-- Navigasi admin -->
  <nav class="bg-blue-600 text-white p-4 flex justify-between items-center shadow-md sticky top-0 z-50">
    <h1 class="text-xl font-bold">
      <i class="fas fa-tachometer-alt mr-2"></i>
      Dashboard Admin
    </h1>
    <div id="navLinks" class="flex items-center">
      <!-- Navigation links will be inserted by JavaScript -->
    </div>
  </nav>

  <!-- Konten utama dashboard -->
  <main class="container mx-auto px-4 py-8">
    <!-- Header dengan statistik -->
    <div class="bg-white rounded-xl shadow-md p-6 mb-8">
      <div class="flex flex-col md:flex-row justify-between items-center">
        <div>
          <h2 class="text-3xl font-bold gradient-text mb-2">Daftar Pengaduan Masuk</h2>
          <p class="text-gray-600" id="adminWelcome">Kelola pengaduan masyarakat dengan mudah</p>
        </div>
        <div id="adminActions" class="hidden mt-4 md:mt-0">
          <a href="register-petugas.html" class="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition duration-300 flex items-center font-medium">
            <i class="fas fa-user-plus mr-2"></i> Tambah Petugas
          </a>
        </div>
      </div>
    </div>

    <!-- Statistik Laporan -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8" id="reportStats">
      <!-- Stats will be inserted by JavaScript -->
    </div>

    <!-- Filter status -->
    <div class="bg-white rounded-xl shadow-md p-4 mb-6">
      <h3 class="text-lg font-semibold text-gray-700 mb-3">Filter Laporan</h3>
      <div class="flex flex-wrap gap-3">
        <button id="filterAll" class="filter-button bg-blue-600 text-white active">
          <i class="fas fa-list-ul mr-2"></i> Semua
        </button>
        <button id="filterPending" class="filter-button bg-gray-200 text-gray-800">
          <i class="fas fa-clock mr-2"></i> Menunggu
        </button>
        <button id="filterVerified" class="filter-button bg-gray-200 text-gray-800">
          <i class="fas fa-check-circle mr-2"></i> Terverifikasi
        </button>
        <button id="filterRejected" class="filter-button bg-gray-200 text-gray-800">
          <i class="fas fa-times-circle mr-2"></i> Ditolak
        </button>
        <button id="filterCompleted" class="filter-button bg-gray-200 text-gray-800">
          <i class="fas fa-check-double mr-2"></i> Selesai
        </button>
      </div>
    </div>

    <!-- Tabel laporan -->
    <div class="table-container bg-white rounded-xl shadow-md overflow-hidden">
      <div class="overflow-x-auto">
        <table class="w-full">
          <thead class="bg-blue-50">
            <tr>
              <th class="text-left px-6 py-3 text-gray-700">ID</th>
              <th class="text-left px-6 py-3 text-gray-700">Judul</th>
              <th class="text-left px-6 py-3 text-gray-700">Pelapor</th>
              <th class="text-left px-6 py-3 text-gray-700">Tanggal</th>
              <th class="text-left px-6 py-3 text-gray-700">Status</th>
              <th class="text-left px-6 py-3 text-gray-700">Aksi</th>
            </tr>
          </thead>
          <tbody id="reportTableBody" class="divide-y divide-gray-100">
            <!-- Report rows will be inserted by JavaScript -->
          </tbody>
        </table>
      </div>
    </div>

    <!-- Empty state message -->
    <div id="emptyState" class="hidden text-center p-10 bg-white rounded-xl shadow-md mt-6 border border-gray-100">
      <div class="mb-6">
        <img src="https://img.freepik.com/free-vector/no-data-concept-illustration_114360-536.jpg" alt="Tidak ada laporan" class="w-64 h-64 mx-auto">
      </div>
      <h3 class="text-xl font-semibold text-gray-800 mb-2">Tidak Ada Laporan</h3>
      <p class="text-gray-500 mb-2 max-w-md mx-auto">Tidak ada laporan yang ditemukan dengan filter yang dipilih.</p>
      <p class="text-gray-500 max-w-md mx-auto">Tunggu hingga ada masyarakat yang mengirimkan laporan atau coba filter lainnya.</p>
    </div>
  </main>

  <!-- JavaScript -->
  <script src="js/app.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Check if user is admin or petugas, redirect if not
      if (!isStaff()) {
        alert('Anda tidak memiliki akses ke halaman ini.');
        window.location.href = 'index.html';
        return;
      }

      // Update title based on role
      const user = getCurrentUser();
      const pageTitle = document.querySelector('h1');
      const adminActions = document.getElementById('adminActions');
      const adminWelcome = document.getElementById('adminWelcome');

      if (isPetugas()) {
        pageTitle.innerHTML = '<i class="fas fa-tachometer-alt mr-2"></i> Dashboard Petugas';
        document.title = 'Dashboard Petugas - Pengaduan Masyarakat';
        adminWelcome.textContent = `Selamat datang, ${user.nama}! Anda login sebagai Petugas.`;
      } else {
        adminWelcome.textContent = `Selamat datang, ${user.nama}! Anda login sebagai Administrator.`;
      }

      // Show admin actions only for admin users
      if (isAdmin()) {
        adminActions.classList.remove('hidden');
      }

      // Get all reports
      let reports = getAllReports();
      let currentFilter = 'all';

      // Calculate report statistics
      const totalReports = reports.length;
      const menungguReports = reports.filter(report => report.status === 'menunggu').length;
      const terverifikasiReports = reports.filter(report => report.status === 'terverifikasi').length;
      const ditolakReports = reports.filter(report => report.status === 'ditolak').length;
      const selesaiReports = reports.filter(report => report.status === 'selesai').length;

      // Display report statistics
      const reportStatsContainer = document.getElementById('reportStats');
      reportStatsContainer.innerHTML = `
        <div class="bg-white rounded-xl shadow-md p-6 hover:shadow-lg transition-all duration-300">
          <div class="flex items-center">
            <div class="bg-blue-100 p-3 rounded-full mr-4">
              <i class="fas fa-file-alt text-blue-600 text-xl"></i>
            </div>
            <div>
              <p class="text-gray-600 text-sm">Total Laporan</p>
              <p class="text-2xl font-bold">${totalReports}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-xl shadow-md p-6 hover:shadow-lg transition-all duration-300">
          <div class="flex items-center">
            <div class="bg-yellow-100 p-3 rounded-full mr-4">
              <i class="fas fa-clock text-yellow-600 text-xl"></i>
            </div>
            <div>
              <p class="text-gray-600 text-sm">Menunggu</p>
              <p class="text-2xl font-bold">${menungguReports}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-xl shadow-md p-6 hover:shadow-lg transition-all duration-300">
          <div class="flex items-center">
            <div class="bg-green-100 p-3 rounded-full mr-4">
              <i class="fas fa-check-circle text-green-600 text-xl"></i>
            </div>
            <div>
              <p class="text-gray-600 text-sm">Terverifikasi</p>
              <p class="text-2xl font-bold">${terverifikasiReports}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-xl shadow-md p-6 hover:shadow-lg transition-all duration-300">
          <div class="flex items-center">
            <div class="bg-blue-100 p-3 rounded-full mr-4">
              <i class="fas fa-check-double text-blue-600 text-xl"></i>
            </div>
            <div>
              <p class="text-gray-600 text-sm">Selesai</p>
              <p class="text-2xl font-bold">${selesaiReports}</p>
            </div>
          </div>
        </div>
      `;

      // Display reports
      function displayReports(reports) {
        const reportTableBody = document.getElementById('reportTableBody');
        const emptyState = document.getElementById('emptyState');

        if (reports.length > 0) {
          let reportsHTML = '';

          reports.forEach(report => {
            // Determine status class, text, and icon
            let statusClass = 'status-menunggu';
            let statusText = 'Menunggu';
            let statusIcon = 'clock';

            // Make sure we're using the correct status value
            if (report.status !== 'menunggu') {
              report.status = report.status.toLowerCase();
            }

            if (report.status === 'terverifikasi') {
              statusClass = 'status-terverifikasi';
              statusText = 'Terverifikasi';
              statusIcon = 'check-circle';
            } else if (report.status === 'ditolak') {
              statusClass = 'status-ditolak';
              statusText = 'Ditolak';
              statusIcon = 'times-circle';
            } else if (report.status === 'selesai') {
              statusClass = 'status-selesai';
              statusText = 'Selesai';
              statusIcon = 'check-double';
            }

            // Create action buttons based on status and role
            let actionButtons = '';

            if (report.status === 'menunggu') {
              // Only admin can verify/reject reports
              if (isAdmin()) {
                actionButtons = `
                  <button onclick="verifyReport('${report.id}')" class="action-button bg-green-500 text-white px-3 py-2 rounded-lg text-sm hover:bg-green-600 mr-2 flex items-center">
                    <i class="fas fa-check mr-1"></i> Verifikasi
                  </button>
                  <button onclick="rejectReport('${report.id}')" class="action-button bg-red-500 text-white px-3 py-2 rounded-lg text-sm hover:bg-red-600 mr-2 flex items-center">
                    <i class="fas fa-times mr-1"></i> Tolak
                  </button>
                  <a href="detail-laporan.html?id=${report.id}" class="action-button inline-block bg-blue-500 text-white px-3 py-2 rounded-lg text-sm hover:bg-blue-600 flex items-center">
                    <i class="fas fa-eye mr-1"></i> Detail
                  </a>
                `;
              } else {
                // Petugas can only view details of pending reports
                actionButtons = `
                  <a href="detail-laporan.html?id=${report.id}" class="action-button inline-block bg-blue-500 text-white px-3 py-2 rounded-lg text-sm hover:bg-blue-600 flex items-center">
                    <i class="fas fa-eye mr-1"></i> Detail
                  </a>
                `;
              }
            } else if (report.status === 'terverifikasi') {
              // Both admin and petugas can respond to verified reports
              actionButtons = `
                <a href="detail-laporan.html?id=${report.id}" class="action-button inline-block bg-blue-500 text-white px-3 py-2 rounded-lg text-sm hover:bg-blue-600 mr-2 flex items-center">
                  <i class="fas fa-eye mr-1"></i> Detail
                </a>
                <a href="tanggapan.html?id=${report.id}" class="action-button inline-block bg-purple-500 text-white px-3 py-2 rounded-lg text-sm hover:bg-purple-600 flex items-center">
                  <i class="fas fa-comment mr-1"></i> Tanggapi
                </a>
              `;
            } else {
              // For completed or rejected reports, only show details
              actionButtons = `
                <a href="detail-laporan.html?id=${report.id}" class="action-button inline-block bg-blue-500 text-white px-3 py-2 rounded-lg text-sm hover:bg-blue-600 flex items-center">
                  <i class="fas fa-eye mr-1"></i> Detail
                </a>
              `;

              // Admin can still respond to any report
              if (isAdmin() && report.status !== 'ditolak') {
                actionButtons += `
                  <a href="tanggapan.html?id=${report.id}" class="action-button ml-2 inline-block bg-purple-500 text-white px-3 py-2 rounded-lg text-sm hover:bg-purple-600 flex items-center">
                    <i class="fas fa-comment mr-1"></i> Tanggapi
                  </a>
                `;
              }
            }

            // Get reporter's full name
            const pelaporFullName = getUserFullName(report.pelapor);

            // Get response count
            const responseCount = report.tanggapan ? report.tanggapan.length : 0;

            // Create table row
            reportsHTML += `
              <tr class="hover:bg-blue-50 transition-colors duration-150">
                <td class="px-6 py-4 text-gray-800">${report.id}</td>
                <td class="px-6 py-4">
                  <div class="font-medium text-gray-800">${report.judul}</div>
                  <div class="text-xs text-gray-500 mt-1 truncate max-w-xs">${report.isi.substring(0, 60)}${report.isi.length > 60 ? '...' : ''}</div>
                </td>
                <td class="px-6 py-4">
                  <div class="flex items-center">
                    <div class="bg-blue-100 w-8 h-8 rounded-full flex items-center justify-center mr-2">
                      <i class="fas fa-user text-blue-600"></i>
                    </div>
                    <span class="text-gray-800">${pelaporFullName}</span>
                  </div>
                </td>
                <td class="px-6 py-4 text-gray-600">${formatDate(report.tanggal)}</td>
                <td class="px-6 py-4">
                  <span class="status-badge ${statusClass}">
                    <i class="fas fa-${statusIcon} mr-1"></i> ${statusText}
                  </span>
                  ${responseCount > 0 ? `
                    <div class="text-xs text-gray-500 mt-1">
                      <i class="fas fa-comment-dots mr-1"></i> ${responseCount} Tanggapan
                    </div>
                  ` : ''}
                </td>
                <td class="px-6 py-4 flex flex-wrap gap-2">
                  ${actionButtons}
                </td>
              </tr>
            `;
          });

          reportTableBody.innerHTML = reportsHTML;
          emptyState.classList.add('hidden');
        } else {
          reportTableBody.innerHTML = '';
          emptyState.classList.remove('hidden');
        }
      }

      // Filter reports
      function filterReports(status) {
        currentFilter = status;

        // Update filter button styles
        document.querySelectorAll('[id^="filter"]').forEach(button => {
          button.classList.remove('bg-blue-600', 'text-white', 'active');
          button.classList.add('bg-gray-200', 'text-gray-800');
        });

        // Get the button ID based on status
        let buttonId;
        if (status === 'all') {
          buttonId = 'filterAll';
        } else if (status === 'menunggu') {
          buttonId = 'filterPending';
        } else if (status === 'terverifikasi') {
          buttonId = 'filterVerified';
        } else if (status === 'ditolak') {
          buttonId = 'filterRejected';
        } else if (status === 'selesai') {
          buttonId = 'filterCompleted';
        }

        // Update the active button style
        if (buttonId) {
          document.getElementById(buttonId).classList.remove('bg-gray-200', 'text-gray-800');
          document.getElementById(buttonId).classList.add('bg-blue-600', 'text-white', 'active');
        }

        // Filter reports
        if (status === 'all') {
          displayReports(reports);
        } else {
          // Normalize status values for comparison
          const filteredReports = reports.filter(report => {
            // Convert status to lowercase for case-insensitive comparison
            const reportStatus = report.status.toLowerCase();
            return reportStatus === status.toLowerCase();
          });
          displayReports(filteredReports);
        }
      }

      // Verify report
      window.verifyReport = function(id) {
        if (confirm('Apakah Anda yakin ingin memverifikasi laporan ini?')) {
          updateReportStatus(id, 'terverifikasi');
          reports = getAllReports(); // Refresh reports

          // Update statistics
          const totalReports = reports.length;
          const menungguReports = reports.filter(report => report.status === 'menunggu').length;
          const terverifikasiReports = reports.filter(report => report.status === 'terverifikasi').length;
          const selesaiReports = reports.filter(report => report.status === 'selesai').length;

          document.querySelectorAll('#reportStats p.text-2xl').forEach((el, index) => {
            if (index === 0) el.textContent = totalReports;
            if (index === 1) el.textContent = menungguReports;
            if (index === 2) el.textContent = terverifikasiReports;
            if (index === 3) el.textContent = selesaiReports;
          });

          filterReports(currentFilter);
        }
      };

      // Reject report
      window.rejectReport = function(id) {
        if (confirm('Apakah Anda yakin ingin menolak laporan ini?')) {
          updateReportStatus(id, 'ditolak');
          reports = getAllReports(); // Refresh reports

          // Update statistics
          const totalReports = reports.length;
          const menungguReports = reports.filter(report => report.status === 'menunggu').length;
          const terverifikasiReports = reports.filter(report => report.status === 'terverifikasi').length;
          const ditolakReports = reports.filter(report => report.status === 'ditolak').length;
          const selesaiReports = reports.filter(report => report.status === 'selesai').length;

          document.querySelectorAll('#reportStats p.text-2xl').forEach((el, index) => {
            if (index === 0) el.textContent = totalReports;
            if (index === 1) el.textContent = menungguReports;
            if (index === 2) el.textContent = terverifikasiReports;
            if (index === 3) el.textContent = selesaiReports;
          });

          filterReports(currentFilter);
        }
      };

      // Add event listeners to filter buttons
      document.getElementById('filterAll').addEventListener('click', () => filterReports('all'));
      document.getElementById('filterPending').addEventListener('click', () => filterReports('menunggu'));
      document.getElementById('filterVerified').addEventListener('click', () => filterReports('terverifikasi'));
      document.getElementById('filterRejected').addEventListener('click', () => filterReports('ditolak'));
      document.getElementById('filterCompleted').addEventListener('click', () => filterReports('selesai'));

      // Initial display
      displayReports(reports);
    });
  </script>
</body>
</html>
