<?php
// check_passwords.php - Check password hashes in database
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Check Password Hashes</h2>";

// Include database configuration
require_once 'config/database.php';

try {
    $pdo = getDatabaseConnection();
    
    // Get all users with their password hashes
    $stmt = $pdo->query("SELECT id, nama, username, password, role, created_at FROM users ORDER BY created_at DESC");
    $users = $stmt->fetchAll();
    
    echo "<h3>Users and Password Verification:</h3>";
    
    if (!empty($users)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f2f2f2;'>";
        echo "<th style='padding: 8px;'>ID</th>";
        echo "<th style='padding: 8px;'>Username</th>";
        echo "<th style='padding: 8px;'>Nama</th>";
        echo "<th style='padding: 8px;'>Role</th>";
        echo "<th style='padding: 8px;'>Password Hash (first 50 chars)</th>";
        echo "<th style='padding: 8px;'>Test 'password123'</th>";
        echo "<th style='padding: 8px;'>Test 'password'</th>";
        echo "</tr>";
        
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>{$user['id']}</td>";
            echo "<td style='padding: 8px;'>{$user['username']}</td>";
            echo "<td style='padding: 8px;'>{$user['nama']}</td>";
            echo "<td style='padding: 8px;'>{$user['role']}</td>";
            echo "<td style='padding: 8px; font-family: monospace; font-size: 10px;'>" . substr($user['password'], 0, 50) . "...</td>";
            
            // Test password123
            $test123 = password_verify('password123', $user['password']);
            echo "<td style='padding: 8px; text-align: center;'>";
            echo $test123 ? "<span style='color: green;'>✓</span>" : "<span style='color: red;'>✗</span>";
            echo "</td>";
            
            // Test password
            $testPassword = password_verify('password', $user['password']);
            echo "<td style='padding: 8px; text-align: center;'>";
            echo $testPassword ? "<span style='color: green;'>✓</span>" : "<span style='color: red;'>✗</span>";
            echo "</td>";
            
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<p><strong>Legend:</strong></p>";
        echo "<ul>";
        echo "<li>✓ = Password cocok</li>";
        echo "<li>✗ = Password tidak cocok</li>";
        echo "</ul>";
        
    } else {
        echo "<p>Tidak ada user ditemukan</p>";
    }
    
    // Test password hashing
    echo "<hr>";
    echo "<h3>Test Password Hashing:</h3>";
    
    $testPasswords = ['password', 'password123', '123456'];
    
    foreach ($testPasswords as $testPass) {
        $hash = password_hash($testPass, PASSWORD_DEFAULT);
        $verify = password_verify($testPass, $hash);
        
        echo "<div style='margin: 10px 0; padding: 10px; border: 1px solid #ddd; background: #f9f9f9;'>";
        echo "<strong>Password:</strong> '$testPass'<br>";
        echo "<strong>Hash:</strong> <span style='font-family: monospace; font-size: 10px;'>$hash</span><br>";
        echo "<strong>Verify:</strong> " . ($verify ? "<span style='color: green;'>✓ Valid</span>" : "<span style='color: red;'>✗ Invalid</span>");
        echo "</div>";
    }
    
    // Manual password test form
    echo "<hr>";
    echo "<h3>Manual Password Test:</h3>";
    echo "<form method='POST' style='border: 1px solid #ddd; padding: 20px; background: #f9f9f9; max-width: 500px;'>";
    echo "<div style='margin-bottom: 15px;'>";
    echo "<label for='testUser' style='display: block; margin-bottom: 5px; font-weight: bold;'>Select User:</label>";
    echo "<select id='testUser' name='test_user' style='width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px;'>";
    echo "<option value=''>-- Pilih User --</option>";
    
    foreach ($users as $user) {
        echo "<option value='{$user['username']}'>{$user['nama']} ({$user['username']})</option>";
    }
    
    echo "</select>";
    echo "</div>";
    echo "<div style='margin-bottom: 15px;'>";
    echo "<label for='testPass' style='display: block; margin-bottom: 5px; font-weight: bold;'>Test Password:</label>";
    echo "<input type='text' id='testPass' name='test_password' placeholder='Masukkan password untuk ditest' style='width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px;'>";
    echo "</div>";
    echo "<button type='submit' name='test_submit' style='padding: 10px 20px; background: #007cba; color: white; border: none; border-radius: 4px; cursor: pointer;'>Test Password</button>";
    echo "</form>";
    
    // Handle manual test
    if (isset($_POST['test_submit']) && !empty($_POST['test_user']) && !empty($_POST['test_password'])) {
        $testUsername = $_POST['test_user'];
        $testPassword = $_POST['test_password'];
        
        $stmt = $pdo->prepare("SELECT username, password FROM users WHERE username = ?");
        $stmt->execute([$testUsername]);
        $user = $stmt->fetch();
        
        if ($user) {
            $isValid = password_verify($testPassword, $user['password']);
            
            echo "<div style='margin: 20px 0; padding: 15px; border-radius: 4px; " . 
                 ($isValid ? "background: #d4edda; border: 1px solid #c3e6cb; color: #155724;" : "background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24;") . "'>";
            echo "<strong>Test Result:</strong><br>";
            echo "Username: $testUsername<br>";
            echo "Password: '$testPassword'<br>";
            echo "Result: " . ($isValid ? "✓ Password BENAR" : "✗ Password SALAH");
            echo "</div>";
        } else {
            echo "<div style='margin: 20px 0; padding: 15px; border-radius: 4px; background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24;'>";
            echo "User tidak ditemukan: $testUsername";
            echo "</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h3>Common Issues:</h3>";
echo "<ul>";
echo "<li><strong>Password tidak cocok:</strong> Pastikan menggunakan password yang benar saat registrasi</li>";
echo "<li><strong>Hash tidak valid:</strong> Mungkin ada masalah saat menyimpan password</li>";
echo "<li><strong>Default passwords:</strong> Admin/Petugas default menggunakan password 'password'</li>";
echo "</ul>";

echo "<h3>Links:</h3>";
echo "<ul>";
echo "<li><a href='test_login.php'>Test Login</a></li>";
echo "<li><a href='login.html'>Login Form</a></li>";
echo "<li><a href='register.html'>Register New User</a></li>";
echo "<li><a href='debug_register.php'>Debug Register</a></li>";
echo "</ul>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; border: 1px solid #ddd; }
th { background-color: #f2f2f2; }
a { color: #007cba; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
