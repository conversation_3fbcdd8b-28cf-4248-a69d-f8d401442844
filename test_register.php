<?php
// test_register.php - Test registrasi langsung tanpa form
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Test Registrasi Langsung</h2>";

// Include database configuration
require_once 'config/database.php';

// Simulate registration data
$testData = [
    'nama' => 'Test User ' . date('Y-m-d H:i:s'),
    'username' => 'testuser' . time(),
    'password' => 'password123',
    'confirmPassword' => 'password123'
];

echo "<h3>Data Test:</h3>";
echo "<ul>";
echo "<li>Nama: {$testData['nama']}</li>";
echo "<li>Username: {$testData['username']}</li>";
echo "<li>Password: {$testData['password']}</li>";
echo "</ul>";

try {
    // Create database connection
    $pdo = getDatabaseConnection();
    echo "<p>✓ Koneksi database berhasil</p>";
    
    // Validate input (copy from register.php)
    $errors = [];
    
    if (empty($testData['nama'])) {
        $errors[] = 'Nama lengkap harus diisi';
    }
    
    if (empty($testData['username'])) {
        $errors[] = 'Username harus diisi';
    } elseif (strlen($testData['username']) < 3) {
        $errors[] = 'Username minimal 3 karakter';
    } elseif (!preg_match('/^[a-zA-Z0-9_]+$/', $testData['username'])) {
        $errors[] = 'Username hanya boleh mengandung huruf, angka, dan underscore (_)';
    }
    
    if (empty($testData['password'])) {
        $errors[] = 'Password harus diisi';
    } elseif (strlen($testData['password']) < 6) {
        $errors[] = 'Password minimal 6 karakter';
    }
    
    if (empty($testData['confirmPassword'])) {
        $errors[] = 'Konfirmasi password harus diisi';
    } elseif ($testData['password'] !== $testData['confirmPassword']) {
        $errors[] = 'Password dan konfirmasi password tidak cocok';
    }
    
    if (!empty($errors)) {
        echo "<p style='color: red;'>✗ Validasi gagal: " . implode(', ', $errors) . "</p>";
        exit;
    }
    
    echo "<p>✓ Validasi berhasil</p>";
    
    // Sanitize input
    $nama = trim($testData['nama']);
    $username = trim(strtolower($testData['username']));
    $password = $testData['password'];
    
    // Check if username already exists
    $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ?");
    $stmt->execute([$username]);
    
    if ($stmt->fetch()) {
        echo "<p style='color: red;'>✗ Username sudah digunakan</p>";
        exit;
    }
    
    echo "<p>✓ Username tersedia</p>";
    
    // Hash password
    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
    echo "<p>✓ Password berhasil di-hash</p>";
    
    // Insert new user
    $stmt = $pdo->prepare("
        INSERT INTO users (nama, username, password, role, created_at) 
        VALUES (?, ?, ?, 'user', NOW())
    ");
    
    $result = $stmt->execute([$nama, $username, $hashedPassword]);
    
    if ($result) {
        $userId = $pdo->lastInsertId();
        echo "<p style='color: green;'>✓ User berhasil disimpan! User ID: $userId</p>";
        
        // Verify the insert
        $stmt = $pdo->prepare("SELECT id, nama, username, role, created_at FROM users WHERE id = ?");
        $stmt->execute([$userId]);
        $newUser = $stmt->fetch();
        
        if ($newUser) {
            echo "<h3>Data User yang Tersimpan:</h3>";
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>Field</th><th>Value</th></tr>";
            echo "<tr><td>ID</td><td>{$newUser['id']}</td></tr>";
            echo "<tr><td>Nama</td><td>{$newUser['nama']}</td></tr>";
            echo "<tr><td>Username</td><td>{$newUser['username']}</td></tr>";
            echo "<tr><td>Role</td><td>{$newUser['role']}</td></tr>";
            echo "<tr><td>Created At</td><td>{$newUser['created_at']}</td></tr>";
            echo "</table>";
            
            echo "<p style='color: green;'>✓ Registrasi berhasil! Sekarang cek di phpMyAdmin</p>";
        } else {
            echo "<p style='color: red;'>✗ User tidak dapat dibaca kembali dari database</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ Gagal menyimpan user ke database</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>✗ Database Error: " . $e->getMessage() . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ General Error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h3>Langkah Selanjutnya:</h3>";
echo "<ol>";
echo "<li>Buka <a href='http://localhost/phpmyadmin' target='_blank'>phpMyAdmin</a></li>";
echo "<li>Pilih database 'pengaduan_masyarakat'</li>";
echo "<li>Klik tabel 'users'</li>";
echo "<li>Lihat apakah user baru sudah muncul</li>";
echo "</ol>";

echo "<h3>Links:</h3>";
echo "<ul>";
echo "<li><a href='debug_register.php'>Debug Register</a></li>";
echo "<li><a href='check_database.php'>Check Database</a></li>";
echo "<li><a href='register.html'>Register Form</a></li>";
echo "</ul>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; border: 1px solid #ddd; }
th { background-color: #f2f2f2; }
a { color: #007cba; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
