<!DOCTYPE html>
<html lang="id">
<head>
  <meta charset="UTF-8">
  <title>Register Petugas - Pengaduan Masyarakat</title>
  <!-- Menggunakan Tailwind CSS untuk styling -->
  <script src="https://cdn.tailwindcss.com"></script>

  <!-- view untuk mobile perangkat mobile -->
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>
<body class="bg-gray-100 font-sans">
  <!-- Navigasi atas -->
  <nav class="bg-blue-600 text-white p-4 flex justify-between">
    <h1 class="text-xl font-bold">Daftar Petugas</h1>
    <div id="navLinks">
      <!-- Navigation links will be inserted by JavaScript -->
    </div>
  </nav>

  <div class="flex items-center justify-center min-h-[calc(100vh-64px)]">
    <!-- Kontainer utama dengan background putih, padding, dan shadow -->
    <div class="bg-white p-8 rounded shadow-md w-full max-w-md m-4">
      <!-- Judul register -->
      <h2 class="text-2xl font-bold mb-6 text-center text-blue-600">Daftar Akun Petugas</h2>
      <p class="text-center text-gray-600 mb-4">Halaman ini khusus untuk admin mendaftarkan petugas baru</p>

      <!-- Form register -->
      <form id="petugasRegisterForm">
        <!-- Input nama -->
        <div class="mb-4">
          <label for="nama" class="block text-gray-700 font-medium mb-2">Nama Lengkap</label>
          <input type="text" id="nama" name="nama" required class="w-full p-2 border border-gray-300 rounded" placeholder="Masukkan nama lengkap">
        </div>

        <!-- Input username -->
        <div class="mb-4">
          <label for="username" class="block text-gray-700 font-medium mb-2">Username</label>
          <input type="text" id="username" name="username" required class="w-full p-2 border border-gray-300 rounded" placeholder="Masukkan username">
        </div>

        <!-- Input password -->
        <div class="mb-4">
          <label for="password" class="block text-gray-700 font-medium mb-2">Password</label>
          <input type="password" id="password" name="password" required class="w-full p-2 border border-gray-300 rounded" placeholder="Masukkan password">
        </div>

        <!-- Input konfirmasi password -->
        <div class="mb-4">
          <label for="confirmPassword" class="block text-gray-700 font-medium mb-2">Konfirmasi Password</label>
          <input type="password" id="confirmPassword" name="confirmPassword" required class="w-full p-2 border border-gray-300 rounded" placeholder="Konfirmasi password">
        </div>

        <!-- Tombol submit -->
        <button type="submit" class="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700">Daftar Petugas</button>
      </form>

      <!-- Tombol kembali -->
      <div class="mt-4 text-center">
        <button id="backButton" class="text-blue-600 hover:underline">Kembali ke Dashboard</button>
      </div>
    </div>
  </div>

  <!-- Memuat file JavaScript eksternal -->
  <script src="js/app.js"></script>
  <script src="js/auth.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Check if user is admin, redirect if not
      if (!isAdmin()) {
        alert('Hanya admin yang dapat mengakses halaman ini.');
        window.location.href = 'dashboard.html';
        return;
      }

      // Back button event listener
      document.getElementById('backButton').addEventListener('click', function() {
        window.location.href = 'dashboard.html';
      });
    });
  </script>
</body>
</html>
