<!DOCTYPE html>
<html lang="id">
<head>
  <meta charset="UTF-8">
  <title>Pengaduan Masyarakat</title>

  <!-- Menggunakan Tailwind CSS untuk styling  -->
  <script src="https://cdn.tailwindcss.com"></script>

  <!-- Font Awesome untuk ikon -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

  <!-- view untuk mobile perangkat mobile -->
  <meta name="viewport" content="width=device-width, initial-scale=1.0">

  <style>
    body {
      font-family: 'Poppins', sans-serif;
    }

    .hero-pattern {
      background-color: #1e40af;
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath opacity='.5' d='M96 95h4v1h-4v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9zm-1 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9z'/%3E%3Cpath d='M6 5V0H5v5H0v1h5v94h1V6h94V5H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    }

    .card-hover:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    .gradient-text {
      background: linear-gradient(90deg, #3b82f6, #1e40af);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      color: transparent;
    }
  </style>
</head>
<body class="bg-gray-50 font-sans">

  <!-- Navigasi atas -->
  <nav class="bg-blue-600 text-white p-4 flex justify-between items-center shadow-md sticky top-0 z-50">
    <h1 class="text-xl font-bold">
      <i class="fas fa-comments mr-2"></i>
      Aplikasi Pengaduan Masyarakat
    </h1>
    <div id="navLinks" class="flex items-center">
      <!-- Navigation links will be inserted by JavaScript -->
    </div>
  </nav>

  <!-- Hero Section -->
  <section class="hero-pattern text-white py-16">
    <div class="container mx-auto px-6">
      <div class="flex flex-col md:flex-row items-center">
        <div class="md:w-1/2 mb-8 md:mb-0">
          <h1 class="text-4xl md:text-5xl font-bold mb-4">Suara Anda Penting Bagi Kami</h1>
          <p class="text-lg mb-6 text-blue-100">Platform pengaduan masyarakat yang memudahkan Anda menyampaikan keluhan dan mendapatkan tanggapan cepat dari petugas kami.</p>
          <div class="flex flex-wrap gap-4">
            <a href="login.html" class="bg-white text-blue-700 hover:bg-blue-50 px-6 py-3 rounded-lg font-semibold transition duration-300 shadow-lg hover:shadow-xl">
              <i class="fas fa-sign-in-alt mr-2"></i> Login
            </a>
            <a href="register.html" class="bg-blue-500 hover:bg-blue-400 text-white px-6 py-3 rounded-lg font-semibold transition duration-300 shadow-lg hover:shadow-xl">
              <i class="fas fa-user-plus mr-2"></i> Daftar
            </a>
          </div>
        </div>
        <div class="md:w-1/2 flex justify-center">
          <img src="https://img.freepik.com/free-vector/customer-support-flat-illustration_23-2148892786.jpg" alt="Ilustrasi Pengaduan" class="w-full max-w-md rounded-lg shadow-2xl">
        </div>
      </div>
    </div>
  </section>

  <!-- Features Section Removed as requested -->

  <!-- Laporan Terbaru Section -->
  <main class="py-12 bg-gray-50">
    <div class="container mx-auto px-6">
      <h2 class="text-3xl font-bold mb-8 text-center gradient-text">Laporan Terbaru</h2>
      <p class="text-center text-gray-600 mb-10 max-w-2xl mx-auto">Berikut adalah laporan terbaru yang telah diverifikasi oleh petugas kami. Anda dapat melihat detail laporan untuk mengetahui informasi lebih lanjut.</p>

      <!-- Grid untuk daftar laporan -->
      <div id="reportContainer" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <!-- Report cards will be inserted by JavaScript -->
      </div>
    </div>
  </main>

  <!-- JavaScript untuk menampilkan laporan -->
  <script src="js/app.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Get reports from localStorage
      const reports = getAllReports();
      const reportContainer = document.getElementById('reportContainer');

      // Display reports
      if (reports.length > 0) {
        // Filter only verified or completed reports for public view
        const publicReports = reports.filter(report =>
          report.status === 'terverifikasi' || report.status === 'selesai'
        );

        if (publicReports.length > 0) {
          let reportHTML = '';

          publicReports.forEach(report => {
            // Determine status color
            let statusColor = 'text-green-600';
            let statusText = 'Terverifikasi';

            if (report.status === 'selesai') {
              statusColor = 'text-blue-600';
              statusText = 'Selesai';
            }

            // Get reporter's full name
            const pelaporFullName = getUserFullName(report.pelapor);

            // Create report card
            reportHTML += `
              <div class="bg-white p-6 rounded-xl shadow-md hover:shadow-xl transition-all duration-300 card-hover border border-gray-100">
                <div class="flex justify-between items-start mb-3">
                  <span class="px-3 py-1 rounded-full text-xs font-semibold ${statusColor} bg-blue-50">${statusText}</span>
                  <span class="text-xs text-gray-500">${formatDate(report.tanggal)}</span>
                </div>
                <h3 class="font-bold text-lg text-gray-800 mb-2 line-clamp-2">${report.judul}</h3>
                <p class="text-gray-600 mb-4 line-clamp-3">${report.isi.substring(0, 150)}${report.isi.length > 150 ? '...' : ''}</p>
                <div class="flex items-center justify-between mt-auto pt-3 border-t border-gray-100">
                  <div class="flex items-center">
                    <div class="bg-blue-100 w-8 h-8 rounded-full flex items-center justify-center mr-2">
                      <i class="fas fa-user text-blue-600"></i>
                    </div>
                    <span class="text-sm text-gray-700">${pelaporFullName}</span>
                  </div>
                  <a href="detail-laporan.html?id=${report.id}" class="text-blue-600 hover:text-blue-800 font-medium flex items-center">
                    Lihat Detail
                    <i class="fas fa-arrow-right ml-1 text-xs"></i>
                  </a>
                </div>
              </div>
            `;
          });

          reportContainer.innerHTML = reportHTML;
        } else {
          reportContainer.innerHTML = `
            <div class="col-span-1 md:col-span-2 lg:col-span-3 text-center p-10 bg-white rounded-xl shadow-md border border-gray-100">
              <div class="mb-6">
                <img src="https://img.freepik.com/free-vector/no-data-concept-illustration_114360-536.jpg" alt="Belum ada laporan" class="w-64 h-64 mx-auto">
              </div>
              <h3 class="text-xl font-semibold text-gray-800 mb-2">Belum Ada Laporan</h3>
              <p class="text-gray-500 mb-6 max-w-md mx-auto">Belum ada laporan yang terverifikasi. Silahkan login untuk membuat laporan baru.</p>
              <a href="login.html" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold transition duration-300 inline-flex items-center">
                <i class="fas fa-edit mr-2"></i> Buat Laporan
              </a>
            </div>
          `;
        }
      } else {
        reportContainer.innerHTML = `
          <div class="col-span-1 md:col-span-2 lg:col-span-3 text-center p-10 bg-white rounded-xl shadow-md border border-gray-100">
            <div class="mb-6">
              <img src="https://img.freepik.com/free-vector/no-data-concept-illustration_114360-536.jpg" alt="Belum ada laporan" class="w-64 h-64 mx-auto">
            </div>
            <h3 class="text-xl font-semibold text-gray-800 mb-2">Belum Ada Laporan</h3>
            <p class="text-gray-500 mb-6 max-w-md mx-auto">Belum ada laporan yang dibuat. Silahkan login untuk membuat laporan baru.</p>
            <a href="login.html" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold transition duration-300 inline-flex items-center">
              <i class="fas fa-edit mr-2"></i> Buat Laporan
            </a>
          </div>
        `;
      }
    });
  </script>

  <!-- Footer Section -->
  <footer class="bg-blue-900 text-white py-12">
    <div class="container mx-auto px-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
        <div class="md:col-span-2">
          <h3 class="text-xl font-bold mb-4">Aplikasi Pengaduan Masyarakat</h3>
          <p class="text-blue-200 mb-4">Platform yang memudahkan masyarakat untuk menyampaikan keluhan dan mendapatkan tanggapan dari petugas dengan cepat dan transparan.</p>
          <div class="flex space-x-4">
            <a href="#" class="text-white hover:text-blue-300">
              <i class="fab fa-facebook-f"></i>
            </a>
            <a href="#" class="text-white hover:text-blue-300">
              <i class="fab fa-twitter"></i>
            </a>
            <a href="#" class="text-white hover:text-blue-300">
              <i class="fab fa-instagram"></i>
            </a>
          </div>
        </div>

        <div>
          <h4 class="text-lg font-semibold mb-4">Tautan</h4>
          <ul class="space-y-2">
            <li><a href="index.html" class="text-blue-200 hover:text-white">Beranda</a></li>
            <li><a href="login.html" class="text-blue-200 hover:text-white">Login</a></li>
            <li><a href="register.html" class="text-blue-200 hover:text-white">Register</a></li>
          </ul>
        </div>

        <div>
          <h4 class="text-lg font-semibold mb-4">Kontak</h4>
          <ul class="space-y-2">
            <li class="flex items-start">
              <i class="fas fa-map-marker-alt mt-1 mr-2 text-blue-300"></i>
              <span class="text-blue-200">Jl. Medan Merdeka No. 123, Kota Jakarta, Indonesia</span>
            </li>
            <li class="flex items-center">
              <i class="fas fa-phone mr-2 text-blue-300"></i>
              <span class="text-blue-200">(021) 1234-5678</span>
            </li>
            <li class="flex items-center">
              <i class="fas fa-envelope mr-2 text-blue-300"></i>
              <span class="text-blue-200"><EMAIL></span>
            </li>
          </ul>
        </div>
      </div>

      <div class="border-t border-blue-800 mt-8 pt-8 text-center text-blue-300">
        <p>&copy; 2025 Aplikasi Pengaduan Masyarakat. All rights reserved.</p>
      </div>
    </div>
  </footer>
</body>
</html>
