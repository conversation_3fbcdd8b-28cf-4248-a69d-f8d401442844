<?php
// debug_register_response.php - Debug register.php response
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Debug Register Response</h2>";

// Test data
$testData = [
    'nama' => 'Test User ' . date('H:i:s'),
    'username' => 'testuser' . time(),
    'password' => 'password123',
    'confirmPassword' => 'password123'
];

echo "<h3>Test Data:</h3>";
echo "<pre>" . json_encode($testData, JSON_PRETTY_PRINT) . "</pre>";

echo "<h3>Testing register.php Response:</h3>";

// Test 1: Direct call to register.php
echo "<h4>Test 1: Direct POST to register.php</h4>";

$jsonData = json_encode($testData);
$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => [
            'Content-Type: application/json',
            'Content-Length: ' . strlen($jsonData)
        ],
        'content' => $jsonData
    ]
]);

$url = 'http://localhost/pengaduan-masyarakat/register.php';
$response = @file_get_contents($url, false, $context);

if ($response !== false) {
    echo "<p style='color: green;'>✓ Response received</p>";
    echo "<h5>Raw Response:</h5>";
    echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd; max-height: 300px; overflow-y: scroll;'>";
    echo htmlspecialchars($response);
    echo "</pre>";
    
    // Try to decode JSON
    $data = json_decode($response, true);
    if ($data !== null) {
        echo "<h5>Parsed JSON:</h5>";
        echo "<pre style='background: #e8f5e8; padding: 10px; border: 1px solid #c3e6cb;'>";
        echo json_encode($data, JSON_PRETTY_PRINT);
        echo "</pre>";
    } else {
        echo "<p style='color: red;'>✗ JSON decode failed: " . json_last_error_msg() . "</p>";
        
        // Check for common issues
        if (strpos($response, '<?php') !== false) {
            echo "<p style='color: red;'>⚠ Response contains PHP code - check for syntax errors</p>";
        }
        if (strpos($response, 'Fatal error') !== false) {
            echo "<p style='color: red;'>⚠ Response contains PHP fatal error</p>";
        }
        if (strpos($response, 'Warning') !== false) {
            echo "<p style='color: red;'>⚠ Response contains PHP warning</p>";
        }
        if (strpos($response, 'Notice') !== false) {
            echo "<p style='color: red;'>⚠ Response contains PHP notice</p>";
        }
    }
} else {
    echo "<p style='color: red;'>✗ Failed to get response</p>";
    $error = error_get_last();
    if ($error) {
        echo "<p>Error: " . $error['message'] . "</p>";
    }
}

// Test 2: Check if register.php is accessible
echo "<hr>";
echo "<h4>Test 2: Check register.php Accessibility</h4>";

$headers = @get_headers($url);
if ($headers) {
    echo "<p style='color: green;'>✓ register.php is accessible</p>";
    echo "<p>HTTP Status: " . $headers[0] . "</p>";
} else {
    echo "<p style='color: red;'>✗ register.php is not accessible</p>";
}

// Test 3: Check database connection
echo "<hr>";
echo "<h4>Test 3: Database Connection</h4>";

try {
    require_once 'config/database.php';
    $pdo = getDatabaseConnection();
    echo "<p style='color: green;'>✓ Database connection successful</p>";
    
    // Test if users table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: green;'>✓ Users table exists</p>";
    } else {
        echo "<p style='color: red;'>✗ Users table does not exist</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Database connection failed: " . $e->getMessage() . "</p>";
}

// Test 4: Manual form test
echo "<hr>";
echo "<h4>Test 4: Manual Registration Test</h4>";
echo "<form id='testForm' style='border: 1px solid #ddd; padding: 20px; background: #f9f9f9; max-width: 400px;'>";
echo "<div style='margin-bottom: 15px;'>";
echo "<label>Nama:</label><br>";
echo "<input type='text' id='nama' value='Test User " . date('H:i:s') . "' style='width: 100%; padding: 8px; margin-top: 5px;'>";
echo "</div>";
echo "<div style='margin-bottom: 15px;'>";
echo "<label>Username:</label><br>";
echo "<input type='text' id='username' value='testuser" . time() . "' style='width: 100%; padding: 8px; margin-top: 5px;'>";
echo "</div>";
echo "<div style='margin-bottom: 15px;'>";
echo "<label>Password:</label><br>";
echo "<input type='password' id='password' value='password123' style='width: 100%; padding: 8px; margin-top: 5px;'>";
echo "</div>";
echo "<div style='margin-bottom: 15px;'>";
echo "<label>Confirm Password:</label><br>";
echo "<input type='password' id='confirmPassword' value='password123' style='width: 100%; padding: 8px; margin-top: 5px;'>";
echo "</div>";
echo "<button type='button' onclick='testRegister()' style='padding: 10px 20px; background: #007cba; color: white; border: none; border-radius: 4px; cursor: pointer;'>Test Register</button>";
echo "</form>";

echo "<div id='testResult' style='margin-top: 20px;'></div>";

echo "<hr>";
echo "<h3>Links:</h3>";
echo "<ul>";
echo "<li><a href='register.html'>Register Form</a></li>";
echo "<li><a href='view_errors.php'>View Error Logs</a></li>";
echo "<li><a href='test_register_form.html'>Test Register Form</a></li>";
echo "</ul>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
pre { font-size: 12px; }
a { color: #007cba; text-decoration: none; }
a:hover { text-decoration: underline; }
.result { margin: 20px 0; padding: 15px; border-radius: 4px; }
.success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
.error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
.debug { background: #f8f9fa; border: 1px solid #dee2e6; color: #495057; font-family: monospace; white-space: pre-wrap; }
</style>

<script>
function testRegister() {
    const resultDiv = document.getElementById('testResult');
    resultDiv.innerHTML = '<div class="debug">Testing registration...</div>';
    
    const formData = {
        nama: document.getElementById('nama').value,
        username: document.getElementById('username').value,
        password: document.getElementById('password').value,
        confirmPassword: document.getElementById('confirmPassword').value
    };
    
    console.log('Sending data:', formData);
    
    fetch('register.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
    })
    .then(response => {
        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers);
        return response.text();
    })
    .then(text => {
        console.log('Raw response:', text);
        
        resultDiv.innerHTML = `
            <div class="debug">
                <strong>Raw Response:</strong><br>
                ${text.replace(/</g, '&lt;').replace(/>/g, '&gt;')}
            </div>
        `;
        
        try {
            const data = JSON.parse(text);
            
            if (data.success) {
                resultDiv.innerHTML += `
                    <div class="success">
                        <strong>✓ Registration Success:</strong> ${data.message}
                    </div>
                `;
            } else {
                resultDiv.innerHTML += `
                    <div class="error">
                        <strong>✗ Registration Failed:</strong> ${data.message}
                    </div>
                `;
            }
            
            resultDiv.innerHTML += `
                <div class="debug">
                    <strong>Parsed JSON:</strong><br>
                    ${JSON.stringify(data, null, 2)}
                </div>
            `;
            
        } catch (e) {
            resultDiv.innerHTML += `
                <div class="error">
                    <strong>✗ JSON Parse Error:</strong> ${e.message}
                </div>
            `;
        }
    })
    .catch(error => {
        console.error('Fetch error:', error);
        resultDiv.innerHTML = `
            <div class="error">
                <strong>✗ Network Error:</strong> ${error.message}
            </div>
        `;
    });
}
</script>
