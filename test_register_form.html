<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <title>Test Register Form</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input { padding: 10px; width: 300px; border: 1px solid #ddd; border-radius: 4px; }
        button { padding: 12px 20px; background: #007cba; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #005a8b; }
        .result { margin: 20px 0; padding: 15px; border-radius: 4px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .debug { background: #f8f9fa; border: 1px solid #dee2e6; color: #495057; font-family: monospace; white-space: pre-wrap; }
    </style>
</head>
<body>
    <h1>Test Registration Form</h1>
    
    <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin-bottom: 20px; border-radius: 4px;">
        <strong>Debug Mode:</strong> Form ini menggunakan register_simple.php untuk debugging
    </div>
    
    <form id="testRegisterForm">
        <div class="form-group">
            <label for="nama">Nama Lengkap:</label>
            <input type="text" id="nama" name="nama" required>
        </div>
        
        <div class="form-group">
            <label for="username">Username:</label>
            <input type="text" id="username" name="username" required>
        </div>
        
        <div class="form-group">
            <label for="password">Password:</label>
            <input type="password" id="password" name="password" required>
        </div>
        
        <div class="form-group">
            <label for="confirmPassword">Konfirmasi Password:</label>
            <input type="password" id="confirmPassword" name="confirmPassword" required>
        </div>
        
        <button type="submit">Test Register</button>
    </form>
    
    <div id="result"></div>
    
    <h3>Test Links:</h3>
    <ul>
        <li><a href="view_errors.php">View Error Logs</a></li>
        <li><a href="debug_register.php">Debug Database</a></li>
        <li><a href="check_database.php">Check Database Status</a></li>
        <li><a href="register.html">Original Register Form</a></li>
    </ul>

    <script>
        // Auto-fill form with test data
        document.getElementById('nama').value = 'Test User ' + new Date().toLocaleTimeString();
        document.getElementById('username').value = 'testuser' + Date.now();
        document.getElementById('password').value = 'password123';
        document.getElementById('confirmPassword').value = 'password123';
        
        document.getElementById('testRegisterForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = {
                nama: document.getElementById('nama').value,
                username: document.getElementById('username').value,
                password: document.getElementById('password').value,
                confirmPassword: document.getElementById('confirmPassword').value
            };
            
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="debug">Sending request...</div>';
            
            // Test with JSON
            fetch('register_simple.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            })
            .then(response => {
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                return response.text();
            })
            .then(text => {
                console.log('Raw response:', text);
                
                try {
                    const data = JSON.parse(text);
                    
                    if (data.success) {
                        resultDiv.innerHTML = `
                            <div class="success">
                                <strong>✓ Success:</strong> ${data.message}
                            </div>
                            <div class="debug">Response: ${JSON.stringify(data, null, 2)}</div>
                        `;
                    } else {
                        resultDiv.innerHTML = `
                            <div class="error">
                                <strong>✗ Error:</strong> ${data.message}
                            </div>
                            <div class="debug">Response: ${JSON.stringify(data, null, 2)}</div>
                        `;
                    }
                } catch (e) {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <strong>✗ JSON Parse Error:</strong> ${e.message}
                        </div>
                        <div class="debug">Raw response: ${text}</div>
                    `;
                }
            })
            .catch(error => {
                console.error('Fetch error:', error);
                resultDiv.innerHTML = `
                    <div class="error">
                        <strong>✗ Network Error:</strong> ${error.message}
                    </div>
                `;
            });
        });
    </script>
</body>
</html>
