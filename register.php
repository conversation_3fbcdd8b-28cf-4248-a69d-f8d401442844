<?php
// register.php - Handle user registration for Pengaduan Masyarakat

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', 'php_errors.log');

// Set content type to JSON
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Include database configuration
require_once 'config/database.php';

// Response function
function sendResponse($success, $message, $data = null) {
    echo json_encode([
        'success' => $success,
        'message' => $message,
        'data' => $data
    ]);
    exit;
}

// Validate input function
function validateInput($data) {
    $errors = [];
    
    // Check if all required fields are present
    if (empty($data['nama'])) {
        $errors[] = 'Nama lengkap harus diisi';
    }
    
    if (empty($data['username'])) {
        $errors[] = 'Username harus diisi';
    } elseif (strlen($data['username']) < 3) {
        $errors[] = 'Username minimal 3 karakter';
    } elseif (!preg_match('/^[a-zA-Z0-9_]+$/', $data['username'])) {
        $errors[] = 'Username hanya boleh mengandung huruf, angka, dan underscore (_)';
    }
    
    if (empty($data['password'])) {
        $errors[] = 'Password harus diisi';
    } elseif (strlen($data['password']) < 6) {
        $errors[] = 'Password minimal 6 karakter';
    }
    
    if (empty($data['confirmPassword'])) {
        $errors[] = 'Konfirmasi password harus diisi';
    } elseif ($data['password'] !== $data['confirmPassword']) {
        $errors[] = 'Password dan konfirmasi password tidak cocok';
    }
    
    return $errors;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendResponse(false, 'Method not allowed');
}

try {
    // Log the request method and data for debugging
    error_log("Registration request method: " . $_SERVER['REQUEST_METHOD']);
    error_log("Raw input: " . file_get_contents('php://input'));
    error_log("POST data: " . json_encode($_POST));

    // Create database connection
    $pdo = getDatabaseConnection();
    error_log("Database connection successful");

    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);

    // If JSON input is empty, try to get from POST data
    if (empty($input)) {
        $input = $_POST;
    }

    error_log("Final input data: " . json_encode($input));
    
    // Validasi
    $errors = validateInput($input);
    if (!empty($errors)) {
        sendResponse(false, implode(', ', $errors));
    }
    
    // Sanitize input
    $nama = trim($input['nama']);
    $username = trim(strtolower($input['username']));
    $password = $input['password'];
    
    // Cek username agar tidak double
    $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ?");
    $stmt->execute([$username]);
    
    if ($stmt->fetch()) {
        sendResponse(false, 'Username sudah digunakan, silakan pilih username lain');
    }
    
    // Hash password (Secure)
    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
    
    // Masukan User Baru
    $stmt = $pdo->prepare("
        INSERT INTO users (nama, username, password, role, created_at) 
        VALUES (?, ?, ?, 'user', NOW())
    ");
    
    $stmt->execute([$nama, $username, $hashedPassword]);
    
    // Get the inserted user ID
    $userId = $pdo->lastInsertId();
    
    sendResponse(true, 'Pendaftaran berhasil! Silakan login dengan akun Anda.', [
        'user_id' => $userId,
        'username' => $username,
        'nama' => $nama
    ]);
    
} catch (PDOException $e) {
    // Log detailed error information
    error_log("PDO Database error: " . $e->getMessage());
    error_log("PDO Error code: " . $e->getCode());
    error_log("PDO Stack trace: " . $e->getTraceAsString());

    // Check if it's a connection error
    if (strpos($e->getMessage(), 'Connection refused') !== false ||
        strpos($e->getMessage(), 'Unknown database') !== false) {
        sendResponse(false, 'Tidak dapat terhubung ke database. Error: ' . $e->getMessage());
    } else {
        sendResponse(false, 'Database error: ' . $e->getMessage());
    }

} catch (Exception $e) {
    error_log("General error: " . $e->getMessage());
    error_log("General error code: " . $e->getCode());
    error_log("General stack trace: " . $e->getTraceAsString());
    sendResponse(false, 'System error: ' . $e->getMessage());
}
?>
