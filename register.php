<?php
// register.php - Handle user registration for Pengaduan Masyarakat

// Disable display errors for clean JSON response
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', 'php_errors.log');

// Set content type to JSON
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Include database configuration
require_once 'config/database.php';

// Response function
function sendResponse($success, $message, $data = null) {
    $response = [
        'success' => $success,
        'message' => $message,
        'data' => $data
    ];
    echo json_encode($response);
    exit;
}

// Validate input function
function validateInput($data) {
    $errors = [];
    
    // Check if all required fields are present
    if (empty($data['nama'])) {
        $errors[] = 'Nama lengkap harus diisi';
    }
    
    if (empty($data['username'])) {
        $errors[] = 'Username harus diisi';
    } elseif (strlen($data['username']) < 3) {
        $errors[] = 'Username minimal 3 karakter';
    } elseif (!preg_match('/^[a-zA-Z0-9_]+$/', $data['username'])) {
        $errors[] = 'Username hanya boleh mengandung huruf, angka, dan underscore (_)';
    }
    
    if (empty($data['password'])) {
        $errors[] = 'Password harus diisi';
    } elseif (strlen($data['password']) < 6) {
        $errors[] = 'Password minimal 6 karakter';
    }
    
    if (empty($data['confirmPassword'])) {
        $errors[] = 'Konfirmasi password harus diisi';
    } elseif ($data['password'] !== $data['confirmPassword']) {
        $errors[] = 'Password dan konfirmasi password tidak cocok';
    }
    
    return $errors;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendResponse(false, 'Method not allowed');
}

try {
    // Get input data
    $rawInput = file_get_contents('php://input');
    $input = json_decode($rawInput, true);

    // If JSON input is empty, try to get from POST data
    if (empty($input)) {
        $input = $_POST;
    }

    // Check if we have any input data
    if (empty($input)) {
        sendResponse(false, 'No input data received');
    }

    // Create database connection
    $pdo = getDatabaseConnection();
    
    // Check required fields first
    $required = ['nama', 'username', 'password', 'confirmPassword'];
    $missing = [];

    foreach ($required as $field) {
        if (empty($input[$field])) {
            $missing[] = $field;
        }
    }

    if (!empty($missing)) {
        sendResponse(false, 'Field yang harus diisi: ' . implode(', ', $missing));
    }

    // Validasi
    $errors = validateInput($input);
    if (!empty($errors)) {
        sendResponse(false, implode(', ', $errors));
    }
    
    // Sanitize input
    $nama = trim($input['nama']);
    $username = trim(strtolower($input['username']));
    $password = $input['password'];
    
    // Cek username agar tidak double
    $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ?");
    $stmt->execute([$username]);
    
    if ($stmt->fetch()) {
        sendResponse(false, 'Username sudah digunakan, silakan pilih username lain');
    }
    
    // Hash password (Secure)
    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
    
    // Masukan User Baru
    $stmt = $pdo->prepare("
        INSERT INTO users (nama, username, password, role, created_at) 
        VALUES (?, ?, ?, 'user', NOW())
    ");
    
    $stmt->execute([$nama, $username, $hashedPassword]);
    
    // Get the inserted user ID
    $userId = $pdo->lastInsertId();
    
    sendResponse(true, 'Pendaftaran berhasil! Silakan login dengan akun Anda.', [
        'user_id' => $userId,
        'username' => $username,
        'nama' => $nama
    ]);
    
} catch (PDOException $e) {
    // Log error for debugging
    error_log("PDO Database error: " . $e->getMessage());

    // Check if it's a connection error
    if (strpos($e->getMessage(), 'Connection refused') !== false ||
        strpos($e->getMessage(), 'Unknown database') !== false) {
        sendResponse(false, 'Tidak dapat terhubung ke database. Pastikan MySQL berjalan.');
    } else {
        sendResponse(false, 'Terjadi kesalahan database. Silakan coba lagi nanti.');
    }

} catch (Exception $e) {
    error_log("General error: " . $e->getMessage());
    sendResponse(false, 'Terjadi kesalahan sistem. Silakan coba lagi nanti.');
}
?>
