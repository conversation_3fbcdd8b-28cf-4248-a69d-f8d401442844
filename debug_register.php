<?php
// debug_register.php - Debug script untuk troubleshoot registrasi
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Debug Registrasi User</h2>";

// Include database configuration
require_once 'config/database.php';

try {
    // Test database connection
    $pdo = getDatabaseConnection();
    echo "<p>✓ Koneksi database berhasil</p>";
    
    // Check if users table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() > 0) {
        echo "<p>✓ Tabel 'users' ditemukan</p>";
        
        // Show current users
        $stmt = $pdo->query("SELECT id, nama, username, role, created_at FROM users ORDER BY created_at DESC");
        $users = $stmt->fetchAll();
        
        echo "<h3>Data Users Saat Ini:</h3>";
        if (empty($users)) {
            echo "<p style='color: orange;'>⚠ Tidak ada user ditemukan</p>";
        } else {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr style='background-color: #f2f2f2;'>";
            echo "<th style='padding: 8px;'>ID</th>";
            echo "<th style='padding: 8px;'>Nama</th>";
            echo "<th style='padding: 8px;'>Username</th>";
            echo "<th style='padding: 8px;'>Role</th>";
            echo "<th style='padding: 8px;'>Created At</th>";
            echo "</tr>";
            
            foreach ($users as $user) {
                echo "<tr>";
                echo "<td style='padding: 8px;'>{$user['id']}</td>";
                echo "<td style='padding: 8px;'>{$user['nama']}</td>";
                echo "<td style='padding: 8px;'>{$user['username']}</td>";
                echo "<td style='padding: 8px;'>{$user['role']}</td>";
                echo "<td style='padding: 8px;'>{$user['created_at']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        // Test insert a dummy user
        echo "<h3>Test Insert User Baru:</h3>";
        
        $testUsername = 'test_user_' . time();
        $testNama = 'Test User ' . date('Y-m-d H:i:s');
        $testPassword = password_hash('password123', PASSWORD_DEFAULT);
        
        $stmt = $pdo->prepare("INSERT INTO users (nama, username, password, role, created_at) VALUES (?, ?, ?, 'user', NOW())");
        $result = $stmt->execute([$testNama, $testUsername, $testPassword]);
        
        if ($result) {
            $newUserId = $pdo->lastInsertId();
            echo "<p style='color: green;'>✓ Test insert berhasil! User ID: $newUserId</p>";
            echo "<p>Username: $testUsername</p>";
            echo "<p>Password: password123</p>";
            
            // Verify the insert
            $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
            $stmt->execute([$newUserId]);
            $newUser = $stmt->fetch();
            
            if ($newUser) {
                echo "<p style='color: green;'>✓ User berhasil disimpan dan dapat dibaca kembali</p>";
            } else {
                echo "<p style='color: red;'>✗ User tidak dapat dibaca kembali</p>";
            }
        } else {
            echo "<p style='color: red;'>✗ Test insert gagal</p>";
        }
        
    } else {
        echo "<p style='color: red;'>✗ Tabel 'users' tidak ditemukan</p>";
        echo "<p>Silakan jalankan <a href='setup_database.php'>setup_database.php</a> terlebih dahulu</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h3>Troubleshooting Steps:</h3>";
echo "<ol>";
echo "<li>Pastikan XAMPP MySQL berjalan</li>";
echo "<li>Jalankan <a href='setup_database.php'>setup_database.php</a> jika database belum ada</li>";
echo "<li>Cek <a href='check_database.php'>check_database.php</a> untuk status database</li>";
echo "<li>Coba registrasi di <a href='register.html'>register.html</a></li>";
echo "<li>Refresh halaman ini untuk melihat user baru</li>";
echo "</ol>";

echo "<h3>Links:</h3>";
echo "<ul>";
echo "<li><a href='setup_database.php'>Setup Database</a></li>";
echo "<li><a href='check_database.php'>Check Database Status</a></li>";
echo "<li><a href='register.html'>Register Form</a></li>";
echo "<li><a href='http://localhost/phpmyadmin' target='_blank'>phpMyAdmin</a></li>";
echo "</ul>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; border: 1px solid #ddd; }
th { background-color: #f2f2f2; }
a { color: #007cba; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
