<!DOCTYPE html>
<html lang="id">
<head>
  <meta charset="UTF-8">
  <title><PERSON><PERSON> Masyarakat</title>

  <!-- Memuat Tailwind CSS dari CDN -->
  <script src="https://cdn.tailwindcss.com"></script>

  <!-- Font Awesome untuk ikon -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

  <!-- view untuk mobile perangkat mobile -->
  <meta name="viewport" content="width=device-width, initial-scale=1.0">

  <style>
    body {
      font-family: 'Poppins', sans-serif;
    }

    .response-bg {
      background-color: #f3f4f6;
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%232563eb' fill-opacity='0.05'%3E%3Cpath opacity='.5' d='M96 95h4v1h-4v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9zm-1 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9z'/%3E%3Cpath d='M6 5V0H5v5H0v1h5v94h1V6h94V5H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    }

    .gradient-text {
      background: linear-gradient(90deg, #3b82f6, #1e40af);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      color: transparent;
    }

    .form-input:focus {
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
    }
  </style>
</head>
<body class="bg-gray-50 font-sans response-bg">
  <!-- Navigasi atas -->
  <nav class="bg-blue-600 text-white p-4 flex justify-between items-center shadow-md sticky top-0 z-50">
    <h1 class="text-xl font-bold">
      <i class="fas fa-comment-dots mr-2"></i>
      Aplikasi Pengaduan Masyarakat
    </h1>
    <div id="navLinks" class="flex items-center">
      <!-- Navigation links will be inserted by JavaScript -->
    </div>
  </nav>

  <!-- Container utama -->
  <div class="container mx-auto px-4 py-8">
    <div class="max-w-2xl mx-auto bg-white p-8 rounded-xl shadow-md">
      <!-- Header dengan tombol kembali -->
      <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold gradient-text">Beri Tanggapan</h2>
        <button id="backButton" class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg transition duration-300 flex items-center">
          <i class="fas fa-arrow-left mr-2"></i> Kembali
        </button>
      </div>

      <!-- Info box -->
      <div class="bg-blue-50 border-l-4 border-blue-500 p-4 mb-6 rounded-r-lg">
        <div class="flex">
          <div class="flex-shrink-0">
            <i class="fas fa-info-circle text-blue-500 text-xl"></i>
          </div>
          <div class="ml-3">
            <p class="text-blue-700 font-medium">Informasi Tanggapan</p>
            <p class="text-blue-600 text-sm mt-1">Berikan tanggapan yang jelas dan informatif. Anda juga dapat mengubah status laporan sesuai dengan tindak lanjut yang dilakukan.</p>
          </div>
        </div>
      </div>

      <!-- Detail laporan -->
      <div id="reportDetail" class="mb-6 p-6 bg-gray-50 rounded-xl border border-gray-100">
        <!-- Report details will be inserted by JavaScript -->
      </div>

      <!-- Formulir tanggapan -->
      <form id="tanggapanForm" class="space-y-6">
        <input type="hidden" id="reportId" name="reportId">

        <!-- Input tanggapan -->
        <div>
          <label for="isiTanggapan" class="block text-gray-700 font-medium mb-2">Isi Tanggapan</label>
          <textarea id="isiTanggapan" name="isiTanggapan"
            class="w-full border border-gray-300 p-3 rounded-lg form-input transition"
            rows="5" placeholder="Tulis tanggapan Anda di sini secara jelas dan informatif..." required></textarea>
          <p class="text-xs text-gray-500 mt-1">Tanggapan ini akan dilihat oleh pelapor</p>
        </div>

        <!-- Dropdown status laporan -->
        <div>
          <label for="status" class="block text-gray-700 font-medium mb-2">Status Laporan</label>
          <div class="relative">
            <select id="status" name="status"
              class="w-full border border-gray-300 p-3 rounded-lg form-input transition appearance-none pr-10" required>
              <option value="terverifikasi">Terverifikasi</option>
              <option value="ditolak">Ditolak</option>
              <option value="selesai">Selesai</option>
            </select>
            <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
              <i class="fas fa-chevron-down text-gray-400"></i>
            </div>
          </div>
          <p class="text-xs text-gray-500 mt-1">Status akan diperbarui setelah tanggapan dikirim</p>
        </div>

        <!-- Tombol submit -->
        <button type="submit"
          class="w-full bg-blue-600 text-white py-3 rounded-lg hover:bg-blue-700 transition duration-300 flex items-center justify-center font-medium">
          <i class="fas fa-paper-plane mr-2"></i> Kirim Tanggapan
        </button>
      </form>
    </div>
  </div>

  <!-- JavaScript -->
  <script src="js/app.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Check if user is admin or petugas, redirect if not
      if (!isStaff()) {
        alert('Anda tidak memiliki akses ke halaman ini.');
        window.location.href = 'index.html';
        return;
      }

      // Update title based on role
      const user = getCurrentUser();
      if (isPetugas()) {
        document.title = 'Beri Tanggapan (Petugas) - Pengaduan Masyarakat';
      }

      // Get report ID from URL
      const urlParams = new URLSearchParams(window.location.search);
      const reportId = urlParams.get('id');

      if (!reportId) {
        alert('ID laporan tidak ditemukan.');
        window.location.href = 'dashboard.html';
        return;
      }

      // Get report details
      const report = getReportById(reportId);

      if (!report) {
        alert('Laporan tidak ditemukan.');
        window.location.href = 'dashboard.html';
        return;
      }

      // Set report ID in form
      document.getElementById('reportId').value = reportId;

      // Get reporter's full name
      const pelaporFullName = getUserFullName(report.pelapor);

      // Determine status class and icon
      let statusClass = '';
      let statusIcon = '';
      let statusText = '';

      if (report.status === 'menunggu') {
        statusClass = 'text-yellow-600 bg-yellow-50';
        statusIcon = 'clock';
        statusText = 'Menunggu';
      } else if (report.status === 'terverifikasi') {
        statusClass = 'text-green-600 bg-green-50';
        statusIcon = 'check-circle';
        statusText = 'Terverifikasi';
      } else if (report.status === 'ditolak') {
        statusClass = 'text-red-600 bg-red-50';
        statusIcon = 'times-circle';
        statusText = 'Ditolak';
      } else if (report.status === 'selesai') {
        statusClass = 'text-blue-600 bg-blue-50';
        statusIcon = 'check-double';
        statusText = 'Selesai';
      }

      // Display report details
      const reportDetail = document.getElementById('reportDetail');
      reportDetail.innerHTML = `
        <div class="flex justify-between items-start mb-4">
          <h3 class="font-bold text-xl text-gray-800">${report.judul}</h3>
          <span class="px-3 py-1 rounded-full text-xs font-semibold ${statusClass}">
            <i class="fas fa-${statusIcon} mr-1"></i> ${statusText}
          </span>
        </div>

        <div class="flex items-start mb-4">
          <div class="bg-blue-100 w-10 h-10 rounded-full flex items-center justify-center mr-3">
            <i class="fas fa-user text-blue-600"></i>
          </div>
          <div>
            <p class="font-medium text-gray-800">Pelapor:</p>
            <p class="text-gray-700">${pelaporFullName}</p>
            <p class="text-xs text-gray-500 mt-1">
              <i class="far fa-calendar-alt mr-1"></i> ${formatDate(report.tanggal)}
            </p>
          </div>
        </div>

        <div class="mb-4">
          <p class="font-medium text-gray-800 mb-2">Deskripsi Laporan:</p>
          <div class="bg-white p-3 rounded-lg border border-gray-200">
            <p class="text-gray-700 whitespace-pre-line">${report.isi}</p>
          </div>
        </div>

        ${report.foto ? `
          <div>
            <p class="font-medium text-gray-800 mb-2">Foto Pendukung:</p>
            <div class="bg-white p-2 rounded-lg border border-gray-200 inline-block">
              <img src="${report.foto}" class="max-h-60 rounded-lg" alt="Foto Laporan">
            </div>
          </div>
        ` : ''}
      `;

      // Handle back button
      const backButton = document.getElementById('backButton');
      backButton.addEventListener('click', function() {
        window.location.href = 'dashboard.html';
      });

      // Handle form submission
      const tanggapanForm = document.getElementById('tanggapanForm');

      tanggapanForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const isiTanggapan = document.getElementById('isiTanggapan').value;
        const status = document.getElementById('status').value;

        // Add tanggapan to report
        const success = addTanggapan(reportId, isiTanggapan, status);

        if (success) {
          alert('Tanggapan berhasil dikirim!');
          window.location.href = 'dashboard.html';
        } else {
          alert('Gagal mengirim tanggapan.');
        }
      });
    });
  </script>
</body>
</html>
