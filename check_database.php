<?php
// check_database.php - Script untuk mengecek status database
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Status Database Pengaduan Masyarakat</h2>";

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';
$dbname = 'pengaduan_masyarakat';

try {
    // Check MySQL server connection
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p>✓ Koneksi ke MySQL server berhasil</p>";
    
    // Check if database exists
    $stmt = $pdo->query("SHOW DATABASES LIKE '$dbname'");
    $database_exists = $stmt->rowCount() > 0;
    
    if ($database_exists) {
        echo "<p>✓ Database '$dbname' ditemukan</p>";
        
        // Connect to the database
        $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Check tables
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        echo "<h3>Tabel yang ditemukan:</h3>";
        if (empty($tables)) {
            echo "<p style='color: orange;'>⚠ Tidak ada tabel ditemukan</p>";
        } else {
            echo "<ul>";
            foreach ($tables as $table) {
                echo "<li>$table</li>";
                
                // Count records in each table
                $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
                $count = $stmt->fetchColumn();
                echo " ($count records)";
            }
            echo "</ul>";
        }
        
        // Check users table specifically
        if (in_array('users', $tables)) {
            echo "<h3>Data Users:</h3>";
            $stmt = $pdo->query("SELECT id, nama, username, role, created_at FROM users");
            $users = $stmt->fetchAll();
            
            if (empty($users)) {
                echo "<p>Tidak ada user ditemukan</p>";
            } else {
                echo "<table border='1' style='border-collapse: collapse;'>";
                echo "<tr><th>ID</th><th>Nama</th><th>Username</th><th>Role</th><th>Created At</th></tr>";
                foreach ($users as $user) {
                    echo "<tr>";
                    echo "<td>{$user['id']}</td>";
                    echo "<td>{$user['nama']}</td>";
                    echo "<td>{$user['username']}</td>";
                    echo "<td>{$user['role']}</td>";
                    echo "<td>{$user['created_at']}</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
        }
        
    } else {
        echo "<p style='color: red;'>✗ Database '$dbname' tidak ditemukan</p>";
        echo "<p>Silakan jalankan <a href='setup_database.php'>setup_database.php</a> untuk membuat database</p>";
    }
    
} catch(PDOException $e) {
    echo "<h3 style='color: red;'>✗ Error: " . $e->getMessage() . "</h3>";
    
    // Common troubleshooting
    echo "<h3>Troubleshooting:</h3>";
    echo "<ol>";
    echo "<li><strong>XAMPP tidak berjalan:</strong> Pastikan Apache dan MySQL di XAMPP Control Panel sudah di-start</li>";
    echo "<li><strong>Port MySQL bermasalah:</strong> Cek apakah port 3306 digunakan aplikasi lain</li>";
    echo "<li><strong>Password MySQL salah:</strong> Cek username/password MySQL di phpMyAdmin</li>";
    echo "<li><strong>Firewall/Antivirus:</strong> Pastikan tidak memblokir koneksi MySQL</li>";
    echo "</ol>";
    
    echo "<h3>Langkah-langkah pengecekan:</h3>";
    echo "<ol>";
    echo "<li>Buka XAMPP Control Panel</li>";
    echo "<li>Pastikan Apache dan MySQL berwarna hijau (running)</li>";
    echo "<li>Klik 'Admin' pada MySQL untuk membuka phpMyAdmin</li>";
    echo "<li>Jika phpMyAdmin terbuka, berarti MySQL berjalan normal</li>";
    echo "</ol>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
</style>
