# Pengaduan Masyarakat - PHP Backend Setup

## Overview
This project now includes PHP backend files for handling user registration, login, and logout with MySQL database integration.

## Files Created

### PHP Backend Files
- `register.php` - Handles user registration
- `login.php` - Handles user authentication
- `logout.php` - Handles user logout
- `config/database.php` - Database configuration and connection
- `database/setup.sql` - Database schema and initial data

### JavaScript Integration
- `js/register-php.js` - Frontend integration for PHP backend

## Database Setup

### 1. Create Database
Run the SQL script to create the database and tables:

```sql
-- Import the setup.sql file or run these commands:
CREATE DATABASE IF NOT EXISTS pengaduan_masyarakat CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE pengaduan_masyarakat;
```

### 2. Import Database Schema
Import the `database/setup.sql` file into your MySQL database:

```bash
mysql -u root -p pengaduan_masyarakat < database/setup.sql
```

Or use phpMyAdmin to import the SQL file.

### 3. Database Configuration
Update the database configuration in `config/database.php` if needed:

```php
$db_config = [
    'host' => 'localhost',
    'dbname' => 'pengaduan_masyarakat',
    'username' => 'root',
    'password' => '',
    'charset' => 'utf8mb4'
];
```

## Default Users
The setup script creates default users:

- **Admin**: username: `admin`, password: `password`
- **Petugas**: username: `petugas`, password: `password`

**Important**: Change these default passwords after setup!

## Usage

### Registration
The `register.php` file handles user registration with:
- Input validation
- Password hashing
- Duplicate username checking
- Secure database insertion

### Login
The `login.php` file handles authentication with:
- Password verification
- Session management
- Role-based redirection

### Frontend Integration
To use the PHP backend instead of localStorage:

1. Include the PHP integration script in your HTML:
```html
<script src="js/register-php.js"></script>
```

2. The script will automatically override the localStorage functionality.

### API Endpoints

#### POST /register.php
Register a new user.

**Request Body:**
```json
{
    "nama": "Full Name",
    "username": "username",
    "password": "password",
    "confirmPassword": "password"
}
```

**Response:**
```json
{
    "success": true,
    "message": "Pendaftaran berhasil! Silakan login dengan akun Anda.",
    "data": {
        "user_id": 1,
        "username": "username",
        "nama": "Full Name"
    }
}
```

#### POST /login.php
Authenticate a user.

**Request Body:**
```json
{
    "username": "username",
    "password": "password"
}
```

**Response:**
```json
{
    "success": true,
    "message": "Login berhasil!",
    "data": {
        "user_id": 1,
        "username": "username",
        "nama": "Full Name",
        "role": "user",
        "redirect_url": "user-dashboard.html"
    }
}
```

#### POST /logout.php
Logout the current user.

**Response:**
```json
{
    "success": true,
    "message": "Logout berhasil!",
    "data": {
        "redirect_url": "index.html"
    }
}
```

## Security Features

- Password hashing using PHP's `password_hash()`
- Prepared statements to prevent SQL injection
- Input validation and sanitization
- Session management
- CORS headers for API access

## Requirements

- PHP 7.4 or higher
- MySQL 5.7 or higher
- Web server (Apache/Nginx)
- PDO MySQL extension

## Development vs Production

### Development
- Error logging is enabled
- CORS allows all origins
- Default database credentials

### Production Recommendations
- Change database credentials
- Restrict CORS origins
- Enable HTTPS
- Set proper error logging
- Change default user passwords
- Use environment variables for sensitive data

## Troubleshooting

### Database Connection Issues
1. Check database credentials in `config/database.php`
2. Ensure MySQL service is running
3. Verify database exists and user has permissions

### Registration/Login Issues
1. Check browser console for JavaScript errors
2. Verify PHP error logs
3. Ensure all required fields are provided
4. Check database table structure

### Session Issues
1. Ensure session support is enabled in PHP
2. Check session cookie settings
3. Verify session storage permissions
