<?php
// fix_passwords.php - Fix password hashes in database
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Fix Password Hashes</h2>";

// Include database configuration
require_once 'config/database.php';

try {
    $pdo = getDatabaseConnection();
    
    // Get all users
    $stmt = $pdo->query("SELECT id, nama, username, password, role FROM users");
    $users = $stmt->fetchAll();
    
    echo "<h3>Current Users and Password Status:</h3>";
    
    if (!empty($users)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f2f2f2;'>";
        echo "<th style='padding: 8px;'>ID</th>";
        echo "<th style='padding: 8px;'>Username</th>";
        echo "<th style='padding: 8px;'>Nama</th>";
        echo "<th style='padding: 8px;'>Role</th>";
        echo "<th style='padding: 8px;'>Password Status</th>";
        echo "<th style='padding: 8px;'>Action</th>";
        echo "</tr>";
        
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>{$user['id']}</td>";
            echo "<td style='padding: 8px;'>{$user['username']}</td>";
            echo "<td style='padding: 8px;'>{$user['nama']}</td>";
            echo "<td style='padding: 8px;'>{$user['role']}</td>";
            
            // Check password format
            $passwordStatus = "Unknown";
            $isHashed = false;
            
            if (strlen($user['password']) === 60 && substr($user['password'], 0, 4) === '$2y$') {
                $passwordStatus = "Properly Hashed";
                $isHashed = true;
            } elseif (strlen($user['password']) < 20) {
                $passwordStatus = "Plain Text (Insecure)";
            } else {
                $passwordStatus = "Unknown Format";
            }
            
            echo "<td style='padding: 8px;'>";
            echo "<span style='color: " . ($isHashed ? "green" : "red") . ";'>$passwordStatus</span>";
            echo "</td>";
            
            echo "<td style='padding: 8px;'>";
            if (!$isHashed) {
                echo "<a href='?fix_user={$user['id']}&password=password123' style='color: #007cba; margin-right: 5px;'>Fix with 'password123'</a>";
                echo "<a href='?fix_user={$user['id']}&password=password' style='color: #007cba;'>Fix with 'password'</a>";
            } else {
                echo "<span style='color: green;'>OK</span>";
            }
            echo "</td>";
            
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Handle password fix
    if (isset($_GET['fix_user']) && isset($_GET['password'])) {
        $userId = (int)$_GET['fix_user'];
        $newPassword = $_GET['password'];
        
        echo "<hr>";
        echo "<h3>Fixing Password for User ID: $userId</h3>";
        
        // Get user info
        $stmt = $pdo->prepare("SELECT username, nama FROM users WHERE id = ?");
        $stmt->execute([$userId]);
        $user = $stmt->fetch();
        
        if ($user) {
            // Hash the password
            $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
            
            // Update password
            $updateStmt = $pdo->prepare("UPDATE users SET password = ? WHERE id = ?");
            $result = $updateStmt->execute([$hashedPassword, $userId]);
            
            if ($result) {
                echo "<p style='color: green;'>✓ Password updated successfully for {$user['username']} ({$user['nama']})</p>";
                echo "<p><strong>New Password:</strong> '$newPassword'</p>";
                echo "<p><strong>Hash:</strong> $hashedPassword</p>";
                
                // Test the new password
                $testResult = password_verify($newPassword, $hashedPassword);
                echo "<p><strong>Verification Test:</strong> " . ($testResult ? "<span style='color: green;'>✓ Success</span>" : "<span style='color: red;'>✗ Failed</span>") . "</p>";
                
                echo "<p><a href='debug_login.php?username={$user['username']}&password=$newPassword'>Test Login with New Password</a></p>";
            } else {
                echo "<p style='color: red;'>✗ Failed to update password</p>";
            }
        } else {
            echo "<p style='color: red;'>✗ User not found</p>";
        }
    }
    
    // Default password setup for admin/petugas
    echo "<hr>";
    echo "<h3>Setup Default Passwords:</h3>";
    echo "<p>Click to set default passwords for admin and petugas accounts:</p>";
    
    $defaultUsers = [
        ['username' => 'admin', 'password' => 'password'],
        ['username' => 'petugas', 'password' => 'password']
    ];
    
    foreach ($defaultUsers as $defaultUser) {
        $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ?");
        $stmt->execute([$defaultUser['username']]);
        $user = $stmt->fetch();
        
        if ($user) {
            echo "<p>";
            echo "<a href='?fix_user={$user['id']}&password={$defaultUser['password']}' style='padding: 8px 16px; background: #007cba; color: white; text-decoration: none; border-radius: 4px;'>";
            echo "Set {$defaultUser['username']} password to '{$defaultUser['password']}'";
            echo "</a>";
            echo "</p>";
        }
    }
    
    // Manual password reset form
    echo "<hr>";
    echo "<h3>Manual Password Reset:</h3>";
    echo "<form method='POST' style='border: 1px solid #ddd; padding: 20px; background: #f9f9f9; max-width: 500px;'>";
    echo "<div style='margin-bottom: 15px;'>";
    echo "<label for='reset_username' style='display: block; margin-bottom: 5px; font-weight: bold;'>Username:</label>";
    echo "<select id='reset_username' name='reset_username' style='width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px;'>";
    echo "<option value=''>-- Select User --</option>";
    
    foreach ($users as $user) {
        echo "<option value='{$user['username']}'>{$user['nama']} ({$user['username']})</option>";
    }
    
    echo "</select>";
    echo "</div>";
    echo "<div style='margin-bottom: 15px;'>";
    echo "<label for='new_password' style='display: block; margin-bottom: 5px; font-weight: bold;'>New Password:</label>";
    echo "<input type='text' id='new_password' name='new_password' placeholder='Enter new password' style='width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px;'>";
    echo "</div>";
    echo "<button type='submit' name='reset_submit' style='padding: 10px 20px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer;'>Reset Password</button>";
    echo "</form>";
    
    // Handle manual password reset
    if (isset($_POST['reset_submit']) && !empty($_POST['reset_username']) && !empty($_POST['new_password'])) {
        $resetUsername = $_POST['reset_username'];
        $newPassword = $_POST['new_password'];
        
        $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
        
        $updateStmt = $pdo->prepare("UPDATE users SET password = ? WHERE username = ?");
        $result = $updateStmt->execute([$hashedPassword, $resetUsername]);
        
        if ($result) {
            echo "<div style='margin: 20px 0; padding: 15px; border-radius: 4px; background: #d4edda; border: 1px solid #c3e6cb; color: #155724;'>";
            echo "<strong>✓ Password Reset Successful</strong><br>";
            echo "Username: $resetUsername<br>";
            echo "New Password: '$newPassword'<br>";
            echo "<a href='debug_login.php?username=$resetUsername&password=$newPassword'>Test Login</a>";
            echo "</div>";
        } else {
            echo "<div style='margin: 20px 0; padding: 15px; border-radius: 4px; background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24;'>";
            echo "✗ Failed to reset password";
            echo "</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h3>Links:</h3>";
echo "<ul>";
echo "<li><a href='debug_login.php'>Debug Login Process</a></li>";
echo "<li><a href='check_passwords.php'>Check Password Hashes</a></li>";
echo "<li><a href='test_login.php'>Test Login</a></li>";
echo "<li><a href='login.html'>Login Page</a></li>";
echo "</ul>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; border: 1px solid #ddd; }
th { background-color: #f2f2f2; }
a { color: #007cba; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
